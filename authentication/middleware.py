"""
JWT Authentication Middleware
Provides automatic token refresh and enhanced authentication handling.
"""

import json
import logging
from django.http import JsonResponse
from django.utils.deprecation import MiddlewareMixin
from django.conf import settings
from rest_framework_simplejwt.authentication import J<PERSON><PERSON>uthentication
from rest_framework_simplejwt.exceptions import InvalidToken, TokenError
from .jwt_utils import is_token_expired, validate_access_token, JWT_ERROR_CODES

logger = logging.getLogger(__name__)


class JWTAuthenticationMiddleware(MiddlewareMixin):
    """
    Middleware to handle JWT authentication and provide helpful error responses.
    This middleware runs before views and can provide better error handling.
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
        self.jwt_auth = JWTAuthentication()
        
        # URLs that don't require authentication
        self.exempt_urls = [
            '/admin/',
            '/api/students/login/',
            '/api/students/register/',
            '/api/contributor/login/',
            '/api/contributor/register/',
            '/api/customrcare/login/',
            '/api/customrcare/register/',
            '/api/students/token/refresh/',
            '/api/contributor/token/refresh/',
            '/api/customrcare/token/refresh/',
            '/static/',
            '/media/',
            '/api/password-reset/',
            '/api/otp-verification/',
            '/api/resend-otp/',
            '/sitemap.xml',
        ]
        
        super().__init__(get_response)
    
    def __call__(self, request):
        # Skip middleware for exempt URLs
        if self.is_exempt_url(request.path):
            return self.get_response(request)
        
        # Process the request
        response = self.process_request(request)
        if response:
            return response
        
        # Continue with the request
        response = self.get_response(request)
        
        # Process the response
        return self.process_response(request, response)
    
    def is_exempt_url(self, path):
        """Check if the URL is exempt from JWT authentication."""
        return any(path.startswith(exempt_url) for exempt_url in self.exempt_urls)
    
    def process_request(self, request):
        """Process the request and handle JWT authentication."""
        # Skip non-API requests
        if not request.path.startswith('/api/'):
            return None
        
        # Get authorization header
        auth_header = request.META.get('HTTP_AUTHORIZATION')
        
        if not auth_header:
            # No auth header - let the view handle it
            return None
        
        try:
            # Parse the authorization header
            token_type, token = auth_header.split(' ', 1)
            if token_type.lower() != 'bearer':
                return self.create_error_response(
                    'INVALID_TOKEN_TYPE',
                    'Authorization header must use Bearer token type'
                )
        except ValueError:
            return self.create_error_response(
                'INVALID_AUTH_HEADER',
                'Invalid authorization header format. Use: Bearer <token>'
            )
        
        # Validate the token
        is_valid, user, error_msg = validate_access_token(token)
        
        if not is_valid:
            if 'expired' in error_msg.lower():
                return self.create_error_response(
                    'TOKEN_EXPIRED',
                    'Access token has expired. Please refresh your token.',
                    include_refresh_hint=True
                )
            else:
                return self.create_error_response(
                    'INVALID_TOKEN',
                    f'Invalid access token: {error_msg}'
                )
        
        # Token is valid, set the user on the request
        request.user = user
        return None
    
    def process_response(self, request, response):
        """Process the response and add helpful headers."""
        # Add CORS headers for JWT responses
        if request.path.startswith('/api/'):
            response['Access-Control-Expose-Headers'] = 'Authorization, Content-Type'
        
        return response
    
    def create_error_response(self, error_code, message, include_refresh_hint=False):
        """Create a standardized error response."""
        error_data = {
            'error': JWT_ERROR_CODES.get(error_code, 'authentication_error'),
            'message': message,
            'code': error_code,
            'timestamp': self.get_current_timestamp()
        }
        
        if include_refresh_hint:
            error_data['hint'] = 'Use the refresh token to obtain a new access token'
            error_data['refresh_endpoints'] = {
                'students': '/api/students/token/refresh/',
                'contributor': '/api/contributor/token/refresh/',
                'customrcare': '/api/customrcare/token/refresh/'
            }
        
        return JsonResponse(error_data, status=401)
    
    def get_current_timestamp(self):
        """Get current timestamp in ISO format."""
        from django.utils import timezone
        return timezone.now().isoformat()


class TokenRefreshMiddleware(MiddlewareMixin):
    """
    Middleware to automatically handle token refresh for expired tokens.
    This is more aggressive and will attempt to refresh tokens automatically.
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
        super().__init__(get_response)
    
    def __call__(self, request):
        # Check if this is a token refresh request
        if 'token/refresh' in request.path:
            return self.get_response(request)
        
        # Check for expired tokens and provide helpful response
        auth_header = request.META.get('HTTP_AUTHORIZATION')
        if auth_header and request.path.startswith('/api/'):
            try:
                token_type, token = auth_header.split(' ', 1)
                if token_type.lower() == 'bearer' and is_token_expired(token):
                    return JsonResponse({
                        'error': 'access_token_expired',
                        'message': 'Your access token has expired',
                        'code': 'TOKEN_EXPIRED',
                        'action_required': 'refresh_token',
                        'refresh_endpoints': {
                            'students': '/api/students/token/refresh/',
                            'contributor': '/api/contributor/token/refresh/',
                            'customrcare': '/api/customrcare/token/refresh/'
                        }
                    }, status=401)
            except (ValueError, AttributeError):
                pass
        
        return self.get_response(request)


class JWTLoggingMiddleware(MiddlewareMixin):
    """
    Middleware to log JWT authentication events for security monitoring.
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
        super().__init__(get_response)
    
    def __call__(self, request):
        # Log authentication attempts
        if request.path.startswith('/api/') and request.method == 'POST':
            if any(endpoint in request.path for endpoint in ['login', 'register', 'token/refresh']):
                self.log_auth_attempt(request)
        
        response = self.get_response(request)
        
        # Log authentication results
        if hasattr(response, 'status_code') and response.status_code == 401:
            self.log_auth_failure(request, response)
        
        return response
    
    def log_auth_attempt(self, request):
        """Log authentication attempts."""
        try:
            client_ip = self.get_client_ip(request)
            user_agent = request.META.get('HTTP_USER_AGENT', 'Unknown')
            
            logger.info(f"JWT Auth attempt: {request.method} {request.path} from {client_ip}")
            logger.debug(f"User-Agent: {user_agent}")
        except Exception as e:
            logger.error(f"Failed to log auth attempt: {e}")
    
    def log_auth_failure(self, request, response):
        """Log authentication failures."""
        try:
            client_ip = self.get_client_ip(request)
            
            # Try to parse error details from response
            error_details = "Unknown error"
            if hasattr(response, 'content'):
                try:
                    error_data = json.loads(response.content.decode('utf-8'))
                    error_details = error_data.get('error', 'Unknown error')
                except:
                    pass
            
            logger.warning(f"JWT Auth failure: {request.path} from {client_ip} - {error_details}")
        except Exception as e:
            logger.error(f"Failed to log auth failure: {e}")
    
    def get_client_ip(self, request):
        """Get the client IP address."""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip
