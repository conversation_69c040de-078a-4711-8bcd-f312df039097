#!/usr/bin/env python3
"""
Test script to debug the subscription API with enhanced logging
"""

import requests
import json
import time

def test_subscription_api():
    """Test the subscription API endpoint"""
    print("🧪 Testing Subscription API with Enhanced Logging")
    print("=" * 55)
    
    # Test data
    test_data = {
        "student": 11,
        "package": 1
    }
    
    print(f"📋 Test Data: {json.dumps(test_data, indent=2)}")
    print(f"🎯 Endpoint: http://127.0.0.1:8000/api/packages/subscriptions/")
    
    try:
        print(f"\n🚀 Sending POST request...")
        response = requests.post(
            "http://127.0.0.1:8000/api/packages/subscriptions/",
            json=test_data,
            timeout=30
        )
        
        print(f"📊 Response Status: {response.status_code}")
        print(f"📄 Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            print(f"✅ SUCCESS!")
            result = response.json()
            print(f"📋 Response Data:")
            print(json.dumps(result, indent=2))
        else:
            print(f"❌ ERROR Response:")
            print(f"Status: {response.status_code}")
            print(f"Content: {response.text}")
            
    except requests.exceptions.Timeout:
        print(f"⏰ Request timed out after 30 seconds")
    except requests.exceptions.ConnectionError:
        print(f"🔌 Connection error - is Django server running?")
    except Exception as e:
        print(f"💥 Unexpected error: {str(e)}")

def test_new_api():
    """Test the new v2 API endpoint"""
    print(f"\n🧪 Testing New V2 API")
    print("=" * 30)
    
    test_data = {
        "student": 11,
        "package": 1
    }
    
    try:
        response = requests.post(
            "http://127.0.0.1:8000/api/packages/v2/create-subscription/",
            json=test_data,
            timeout=30
        )
        
        print(f"📊 V2 API Status: {response.status_code}")
        
        if response.status_code == 201:
            print(f"✅ V2 API SUCCESS!")
            result = response.json()
            print(f"📋 V2 Response Data:")
            print(json.dumps(result, indent=2))
        else:
            print(f"❌ V2 API ERROR:")
            print(f"Status: {response.status_code}")
            print(f"Content: {response.text}")
            
    except Exception as e:
        print(f"💥 V2 API error: {str(e)}")

if __name__ == "__main__":
    print("🔍 SUBSCRIPTION API DEBUG TEST")
    print("=" * 50)
    print("This script will test both old and new subscription APIs")
    print("Check Django server logs for detailed debugging information")
    print("=" * 50)
    
    # Test old API
    test_subscription_api()
    
    # Wait a bit
    time.sleep(2)
    
    # Test new API
    test_new_api()
    
    print(f"\n📝 Next Steps:")
    print(f"1. Check Django server logs for detailed error information")
    print(f"2. Look for '=== SUBSCRIPTION CREATION START ===' in logs")
    print(f"3. Follow the log trail to identify the exact failure point")
    print(f"4. Check database for any created records")
