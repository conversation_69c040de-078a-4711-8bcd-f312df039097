{"info": {"name": "Subscription API v2 - Complete Testing", "description": "Comprehensive testing collection for Subscription API v2 endpoints", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "BASE_URL", "value": "http://127.0.0.1:8000", "type": "string"}, {"key": "API_BASE", "value": "{{BASE_URL}}/api/packages", "type": "string"}], "item": [{"name": "Package Management", "item": [{"name": "GET Package List", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{API_BASE}}/", "host": ["{{API_BASE}}"], "path": [""]}, "description": "Retrieve all available packages"}, "response": []}, {"name": "GET Razorpay Config", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{API_BASE}}/razorpay-config/", "host": ["{{API_BASE}}"], "path": ["razorpay-config", ""]}, "description": "Get Razor<PERSON>y configuration for frontend"}, "response": []}]}, {"name": "Subscription Creation v2", "item": [{"name": "POST Basic Subscription", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"student\": 11,\n  \"package\": 1\n}"}, "url": {"raw": "{{API_BASE}}/v2/create-subscription/", "host": ["{{API_BASE}}"], "path": ["v2", "create-subscription", ""]}, "description": "Create basic subscription without coupon or gift card"}, "response": []}, {"name": "POST Subscription with Coupon", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"student\": 11,\n  \"package\": 3,\n  \"coupon\": \"SAVE20\"\n}"}, "url": {"raw": "{{API_BASE}}/v2/create-subscription/", "host": ["{{API_BASE}}"], "path": ["v2", "create-subscription", ""]}, "description": "Create subscription with coupon code"}, "response": []}, {"name": "POST Subscription with Gift Card", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"student\": 11,\n  \"package\": 1,\n  \"gift_card_code\": \"GIFT123\",\n  \"gift_card_pin\": \"1234\"\n}"}, "url": {"raw": "{{API_BASE}}/v2/create-subscription/", "host": ["{{API_BASE}}"], "path": ["v2", "create-subscription", ""]}, "description": "Create subscription using gift card"}, "response": []}, {"name": "POST Event Package Subscription", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"student\": 11,\n  \"package\": 4\n}"}, "url": {"raw": "{{API_BASE}}/v2/create-subscription/", "host": ["{{API_BASE}}"], "path": ["v2", "create-subscription", ""]}, "description": "Create subscription for event package"}, "response": []}]}, {"name": "Payment Processing v2", "item": [{"name": "POST Verify Payment", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"razorpay_order_id\": \"order_QyS0J51dkl5RY2\",\n  \"razorpay_payment_id\": \"pay_QyS0J51dkl5RY3\",\n  \"razorpay_signature\": \"generated_signature_hash\",\n  \"subscription_id\": 7\n}"}, "url": {"raw": "{{API_BASE}}/v2/verify-payment/", "host": ["{{API_BASE}}"], "path": ["v2", "verify-payment", ""]}, "description": "Verify Razorpay payment and activate subscription"}, "response": []}, {"name": "GET Subscription Status", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{API_BASE}}/v2/subscription-status/11/", "host": ["{{API_BASE}}"], "path": ["v2", "subscription-status", "11", ""]}, "description": "Check subscription status for a student"}, "response": []}]}, {"name": "<PERSON><PERSON><PERSON>", "item": [{"name": "POST Invalid Student ID", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"student\": 99999,\n  \"package\": 1\n}"}, "url": {"raw": "{{API_BASE}}/v2/create-subscription/", "host": ["{{API_BASE}}"], "path": ["v2", "create-subscription", ""]}, "description": "Test with invalid student ID - should return 404"}, "response": []}, {"name": "POST Invalid Package ID", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"student\": 11,\n  \"package\": 99999\n}"}, "url": {"raw": "{{API_BASE}}/v2/create-subscription/", "host": ["{{API_BASE}}"], "path": ["v2", "create-subscription", ""]}, "description": "Test with invalid package ID - should return 404"}, "response": []}, {"name": "POST Missing Required Fields", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"student\": 11\n}"}, "url": {"raw": "{{API_BASE}}/v2/create-subscription/", "host": ["{{API_BASE}}"], "path": ["v2", "create-subscription", ""]}, "description": "Test with missing package field - should return 400"}, "response": []}, {"name": "POST Invalid Coupon Code", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"student\": 11,\n  \"package\": 1,\n  \"coupon\": \"INVALID_COUPON\"\n}"}, "url": {"raw": "{{API_BASE}}/v2/create-subscription/", "host": ["{{API_BASE}}"], "path": ["v2", "create-subscription", ""]}, "description": "Test with invalid coupon code - should return 400"}, "response": []}]}]}