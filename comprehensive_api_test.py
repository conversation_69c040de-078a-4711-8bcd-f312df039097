#!/usr/bin/env python3
"""
Comprehensive test of the fixed subscription API
"""

import requests
import json
import time

def test_old_api():
    """Test the old subscription API endpoint"""
    print("🧪 Testing OLD API: /api/packages/subscriptions/")
    print("-" * 50)
    
    test_data = {"student": 11, "package": 1}
    
    try:
        response = requests.post(
            "http://127.0.0.1:8000/api/packages/subscriptions/",
            json=test_data,
            timeout=15
        )
        
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print("✅ SUCCESS!")
            print(f"Subscription ID: {result.get('subscription_id')}")
            print(f"Final Price: ₹{result.get('final_price')}")
            print(f"Razorpay Order: {result.get('razorpay_order', {}).get('id', 'N/A')}")
            return True
        else:
            print(f"❌ FAILED: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return False

def test_new_api():
    """Test the new v2 subscription API endpoint"""
    print("\n🧪 Testing NEW API: /api/packages/v2/create-subscription/")
    print("-" * 55)
    
    test_data = {"student": 11, "package": 3}  # Different package
    
    try:
        response = requests.post(
            "http://127.0.0.1:8000/api/packages/v2/create-subscription/",
            json=test_data,
            timeout=15
        )
        
        print(f"Status: {response.status_code}")
        if response.status_code == 201:
            result = response.json()
            print("✅ SUCCESS!")
            print(f"Subscription ID: {result.get('subscription_id')}")
            print(f"Final Price: ₹{result.get('final_price')}")
            print(f"Razorpay Order: {result.get('razorpay_order_id', 'N/A')}")
            print(f"Package Type: {result.get('package_type')}")
            return True
        else:
            print(f"❌ FAILED: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return False

def test_package_listing():
    """Test package listing endpoint"""
    print("\n🧪 Testing Package Listing: /api/packages/")
    print("-" * 45)
    
    try:
        response = requests.get("http://127.0.0.1:8000/api/packages/", timeout=10)
        
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            packages = response.json()
            print(f"✅ SUCCESS! Found {len(packages)} packages")
            for pkg in packages[:3]:
                print(f"  - {pkg['name']} (₹{pkg['discount_price']})")
            return True
        else:
            print(f"❌ FAILED: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return False

def test_razorpay_config():
    """Test Razorpay configuration endpoint"""
    print("\n🧪 Testing Razorpay Config: /api/packages/razorpay-config/")
    print("-" * 55)
    
    try:
        response = requests.get("http://127.0.0.1:8000/api/packages/razorpay-config/", timeout=10)
        
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            config = response.json()
            print("✅ SUCCESS!")
            print(f"Razorpay Key: {config.get('razorpay_key')}")
            print(f"Currency: {config.get('currency')}")
            return True
        else:
            print(f"❌ FAILED: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return False

def main():
    """Run comprehensive API tests"""
    print("🚀 COMPREHENSIVE API TESTING")
    print("=" * 60)
    print("Testing all fixed subscription API endpoints")
    print("=" * 60)
    
    results = []
    
    # Test all endpoints
    results.append(("Package Listing", test_package_listing()))
    results.append(("Razorpay Config", test_razorpay_config()))
    results.append(("Old Subscription API", test_old_api()))
    time.sleep(2)  # Small delay between tests
    results.append(("New Subscription API", test_new_api()))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:<25} {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ The subscription API is working correctly")
        print("✅ Both old and new endpoints are functional")
        print("✅ Razorpay integration is working")
        print("✅ Package listing is working")
        print("\n🎯 Ready for production use!")
    else:
        print(f"\n⚠️  {total - passed} test(s) failed")
        print("Check the errors above for details")
    
    print("\n📝 What was fixed:")
    print("- Added comprehensive error handling and logging")
    print("- Fixed email sending errors that caused 500 responses")
    print("- Improved gift card and coupon processing")
    print("- Enhanced referral reward processing")
    print("- Added detailed logging for debugging")
    print("- Fixed subscription creation and invoice generation")

if __name__ == "__main__":
    main()
