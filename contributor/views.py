from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework.permissions import IsAuthenticated

from blogs.models import BlogPost
from contributor.models import ContributorPoints, ContributorProfile, ContributorEarning
from contributor.earning_service import EarningCalculationService
from questions.models import (
    MasterOption,
    MasterQuestion,
    PreviousYearQuestion,
    Question,
)
from .serializers import (
    ContributorProfileSerializer,
    ContributorRegistrationSerializer,
    LoginSerializer,
    ContributorEarningSerializer,
    ContributorEarningSummarySerializer,
    ContributorPointsSerializer,
    ContributorProfileEarningSerializer,
)

from .permissions import (
    IsCont<PERSON>butor<PERSON>ser,
    IsCustomerCareOrReadOnly,
    IsContributorForPopupBanner,
    IsCustomerCareForPopupBanner,
    IsAdminForPopupBanner,
    IsContributorOrCustomerCareOrAdmin,
    IsP<PERSON>lic<PERSON><PERSON>Only
)
from django.utils.timezone import now, timed<PERSON>ta
from rest_framework.status import HTTP_200_OK, HTTP_400_BAD_REQUEST
from django.shortcuts import get_object_or_404
    
from rest_framework import generics
from .models import Banner, PageCounter, PopupBanner
from .serializers import (
    BannerSerializer,
    PopupBannerContributorSerializer,
    PopupBannerCustomerCareSerializer,
    PopupBannerAdminSerializer,
    PopupBannerPublicSerializer
)
from rest_framework.decorators import api_view
import json
from django.contrib.contenttypes.models import ContentType
from django.db.models import Count
from .throttles import LoginRateThrottle, RegisterRateThrottle
from rest_framework.decorators import api_view, throttle_classes

@throttle_classes([RegisterRateThrottle])
class RegisterView(APIView):

    def post(self, request):
        # breakpoint()
        serializer = ContributorRegistrationSerializer(data=request.data)
        if serializer.is_valid():
            user = serializer.save()
            refresh = RefreshToken.for_user(user)
            return Response(
                {
                    "profile": serializer.data,
                    "refresh": str(refresh),
                    "access": str(refresh.access_token),
                    "message": "User registered successfully",
                },
                status=status.HTTP_201_CREATED,
            )
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def get(self, request, slug=None):
        """Retrieve a contributor profile or list all profiles."""
        if slug:
            profile = get_object_or_404(ContributorProfile, slug=slug)
            serializer = ContributorProfileSerializer(profile)
            return Response(serializer.data, status=status.HTTP_200_OK)
        profiles = ContributorProfile.objects.all()[::-1]
        serializer = ContributorProfileSerializer(profiles, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)

    def put(self, request, slug):
        """Update a contributor profile."""
        profile = get_object_or_404(ContributorProfile, slug=slug)
        serializer = ContributorRegistrationSerializer(
            profile, data=request.data, partial=True
        )

        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_200_OK)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request, slug):
        """Delete a contributor profile."""
        profile = get_object_or_404(ContributorProfile, slug=slug)
        profile.user.delete()  # Deletes the related user account
        profile.delete()
        return Response(
            {"message": "Profile deleted successfully"},
            status=status.HTTP_204_NO_CONTENT,
        )

from log_admin.models import UserActivity
@throttle_classes([LoginRateThrottle])
class LoginView(APIView):
    def post(self, request):
        serializer = LoginSerializer(data=request.data)
        if serializer.is_valid():
            user = serializer.validated_data["user"]
            role = serializer.validated_data["role"]
            contributor_profile = user.contributor_profile
            refresh = RefreshToken.for_user(user)
            if role == "contributor":
                contributor_profile = getattr(user, 'contributor_profile', None)
                UserActivity.objects.create(user=user, action="Contributor Login", metadata={"path": request.path})

                return Response(
                    {
                        "profile": {
                            "username": user.username,
                            "email": user.email,
                            "first_name": user.first_name,
                            "last_name": user.last_name,
                        },
                        "contributor_profile_id": contributor_profile.id,
                        "role": role,
                        "refresh": str(refresh),
                        "access": str(refresh.access_token),
                    },
                    status=status.HTTP_200_OK,
                )
        return Response(serializer.errors, status=status.HTTP_401_UNAUTHORIZED)


class LogoutView(APIView):
    permission_classes = (IsContributorUser,)

    def post(self, request):
        try:
            refresh_token = request.data["refresh"]
            token = RefreshToken(refresh_token)
            token.blacklist()
            return Response(
                {"message": "Logged out successfully"},
                status=status.HTTP_204_NO_CONTENT,
            )
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


# Import the enhanced token refresh view
from authentication.jwt_utils import EnhancedTokenRefreshView

class TokenRefreshView(EnhancedTokenRefreshView):
    """
    Enhanced token refresh view for contributor app.
    Inherits from EnhancedTokenRefreshView for consistent behavior.
    """
    pass


class ContributorDashboardAPIView(APIView):
    permission_classes = (IsAuthenticated,)

    def get(self, request):
        try:
            # Get the logged-in contributor
            contributor = ContributorProfile.objects.get(user=request.user)

            # Current date and time
            today = now().date()
            month_start = today.replace(day=1)
            week_start = today - timedelta(days=today.weekday())
            previous_month_start = (month_start - timedelta(days=1)).replace(day=1)
            previous_month_end = month_start - timedelta(days=1)
            third_month_start = (previous_month_start - timedelta(days=1)).replace(
                day=1
            )
            third_month_end = previous_month_start - timedelta(days=1)

            # Fetch all related data created by the contributor
            all_questions = Question.objects.filter(author=contributor)
            master_questions = MasterQuestion.objects.filter(author=contributor)
            master_options = MasterOption.objects.filter(author=contributor)
            blogs = BlogPost.objects.filter(author=contributor)
            previous_questions = PreviousYearQuestion.objects.filter(
                question__author=contributor
            )

            # Function to get counts for each status
            def get_status_counts(queryset):
                return {
                    "created": queryset.count(),
                    "approved": queryset.filter(approval_status="approved").count(),
                    "pending": queryset.filter(approval_status="pending").count(),
                    "rejected": queryset.filter(approval_status="rejected").count(),
                }

            # Summaries for all data types
            data_summary = {
                "questions": {
                    "total": get_status_counts(all_questions),
                },
                "master_questions": {
                    "total": get_status_counts(master_questions),
                },
                "master_options": {
                    "total": get_status_counts(master_options),
                },
                "blogs": {
                    "total": get_status_counts(blogs),
                },
                "previous_questions": {
                    "total": get_status_counts(previous_questions),
                },
            }

            # Filters for each time period
            def get_period_data(queryset, period_start, period_end=None):
                if period_end:
                    return queryset.filter(
                        created_at__date__gte=period_start,
                        created_at__date__lte=period_end,
                    )
                return queryset.filter(created_at__date__gte=period_start)

            current_month_data = {
                "questions": {
                    "daily": get_status_counts(
                        all_questions.filter(created_at__date=today)
                    ),
                    "weekly": get_status_counts(
                        get_period_data(all_questions, week_start)
                    ),
                    "monthly": get_status_counts(
                        get_period_data(all_questions, month_start)
                    ),
                },
                "master_questions": {
                    "daily": get_status_counts(
                        master_questions.filter(created_at__date=today)
                    ),
                    "weekly": get_status_counts(
                        get_period_data(master_questions, week_start)
                    ),
                    "monthly": get_status_counts(
                        get_period_data(master_questions, month_start)
                    ),
                },
                "blogs": {
                    "daily": get_status_counts(blogs.filter(created_at__date=today)),
                    "weekly": get_status_counts(get_period_data(blogs, week_start)),
                    "monthly": get_status_counts(get_period_data(blogs, month_start)),
                },
                "master_options": {
                    "daily": get_status_counts(
                        master_options.filter(created_at__date=today)
                    ),
                    "weekly": get_status_counts(
                        get_period_data(master_options, week_start)
                    ),
                    "monthly": get_status_counts(
                        get_period_data(master_options, month_start)
                    ),
                },
                "previous_questions": {
                    "daily": get_status_counts(
                        previous_questions.filter(created_at__date=today)
                    ),
                    "weekly": get_status_counts(
                        get_period_data(previous_questions, week_start)
                    ),
                    "monthly": get_status_counts(
                        get_period_data(previous_questions, month_start)
                    ),
                },
            }

            previous_month_data = {
                "questions": {
                    "monthly": get_status_counts(
                        get_period_data(
                            all_questions, previous_month_start, previous_month_end
                        )
                    ),
                },
                "master_questions": {
                    "monthly": get_status_counts(
                        get_period_data(
                            master_questions, previous_month_start, previous_month_end
                        )
                    ),
                },
                "blogs": {
                    "monthly": get_status_counts(
                        get_period_data(blogs, previous_month_start, previous_month_end)
                    ),
                },
                "master_options": {
                    "monthly": get_status_counts(
                        get_period_data(
                            master_options, previous_month_start, previous_month_end
                        )
                    ),
                },
                "previous_questions": {
                    "monthly": get_status_counts(
                        get_period_data(
                            previous_questions, previous_month_start, previous_month_end
                        )
                    ),
                },
            }

            third_month_data = {
                "questions": {
                    "monthly": get_status_counts(
                        get_period_data(
                            all_questions, third_month_start, third_month_end
                        )
                    ),
                },
                "master_questions": {
                    "monthly": get_status_counts(
                        get_period_data(
                            master_questions, third_month_start, third_month_end
                        )
                    ),
                },
                "blogs": {
                    "monthly": get_status_counts(
                        get_period_data(blogs, third_month_start, third_month_end)
                    ),
                },
                "master_options": {
                    "monthly": get_status_counts(
                        get_period_data(
                            master_options, third_month_start, third_month_end
                        )
                    ),
                },
                "previous_questions": {
                    "monthly": get_status_counts(
                        get_period_data(
                            previous_questions, third_month_start, third_month_end
                        )
                    ),
                },
            }

            # Points calculation helper
            def calculate_points(queryset, multiplier):
                return queryset.count() * multiplier

            # Get points multipliers from the database
            contributor_points = ContributorPoints.objects.last()
            if contributor_points:

                # Current month points
                current_month_points = {
                    "normal_questions": calculate_points(
                        get_period_data(all_questions, month_start),
                        contributor_points.normal_questions,
                    ),
                    "master_questions": calculate_points(
                        get_period_data(master_questions, month_start),
                        contributor_points.master_questions,
                    ),
                    "master_options": calculate_points(
                        get_period_data(master_options, month_start),
                        contributor_points.master_options,
                    ),
                    "blogs": calculate_points(
                        get_period_data(blogs, month_start),
                        contributor_points.blogs,
                    ),
                    "previous_questions": calculate_points(
                        get_period_data(previous_questions, month_start),
                        contributor_points.previous_questions,
                    ),
                }
                current_month_points["total_points"] = sum(current_month_points.values())

                # Previous month points
                previous_month_points = {
                    "normal_questions": calculate_points(
                        get_period_data(
                            all_questions, previous_month_start, previous_month_end
                        ),
                        contributor_points.normal_questions,
                    ),
                    "master_questions": calculate_points(
                        get_period_data(
                            master_questions, previous_month_start, previous_month_end
                        ),
                        contributor_points.master_questions,
                    ),
                    "master_options": calculate_points(
                        get_period_data(
                            master_options, previous_month_start, previous_month_end
                        ),
                        contributor_points.master_options,
                    ),
                    "blogs": calculate_points(
                        get_period_data(blogs, previous_month_start, previous_month_end),
                        contributor_points.blogs,
                    ),
                    "previous_questions": calculate_points(
                        get_period_data(
                            previous_questions, previous_month_start, previous_month_end
                        ),
                        contributor_points.previous_questions,
                    ),
                }
                previous_month_points["total_points"] = sum(previous_month_points.values())

                # Third month points
                third_month_points = {
                    "normal_questions": calculate_points(
                        get_period_data(all_questions, third_month_start, third_month_end),
                        contributor_points.normal_questions,
                    ),
                    "master_questions": calculate_points(
                        get_period_data(
                            master_questions, third_month_start, third_month_end
                        ),
                        contributor_points.master_questions,
                    ),
                    "master_options": calculate_points(
                        get_period_data(master_options, third_month_start, third_month_end),
                        contributor_points.master_options,
                    ),
                    "blogs": calculate_points(
                        get_period_data(blogs, third_month_start, third_month_end),
                        contributor_points.blogs,
                    ),
                    "previous_questions": calculate_points(
                        get_period_data(
                            previous_questions, third_month_start, third_month_end
                        ),
                        contributor_points.previous_questions,
                    ),
                }
                third_month_points["total_points"] = sum(third_month_points.values())

                # Calculate and get earning information
                try:
                    # Update current month earnings
                    current_earning = EarningCalculationService.calculate_and_update_earnings(
                        contributor, 'monthly'
                    )

                    # Get earnings summary for different periods
                    current_month_earnings = EarningCalculationService.get_earnings_summary(
                        contributor, 'monthly'
                    )

                    # Get total lifetime earnings
                    total_earnings_data = EarningCalculationService.get_contributor_total_earnings(
                        contributor
                    )

                    # Get points configuration being used
                    points_config = contributor.get_points_config()
                    points_config_info = {
                        'name': points_config.name if points_config else 'Default',
                        'is_custom': bool(contributor.custom_points),
                        'normal_questions': points_config.normal_questions if points_config else 0,
                        'master_questions': points_config.master_questions if points_config else 0,
                        'master_options': points_config.master_options if points_config else 0,
                        'blogs': points_config.blogs if points_config else 0,
                        'previous_questions': points_config.previous_questions if points_config else 0,
                    }

                    earning_info = {
                        'current_month_earnings': current_month_earnings,
                        'total_lifetime_earnings': total_earnings_data,
                        'points_configuration': points_config_info,
                        'earning_status': {
                            'is_paid': current_earning.is_paid,
                            'paid_at': current_earning.paid_at,
                        }
                    }

                except Exception as e:
                    earning_info = {
                        'error': f"Error calculating earnings: {str(e)}",
                        'current_month_earnings': None,
                        'total_lifetime_earnings': None,
                        'points_configuration': None,
                    }

                # Prepare response
                response_data = {
                    "contributor": contributor.user.username,
                    "questions_summary": data_summary,
                    "current_month_data": current_month_data,
                    "previous_month_data": previous_month_data,
                    "third_month_data": third_month_data,
                    "current_month_points": current_month_points,
                    "previous_month_points": previous_month_points,
                    "third_month_points": third_month_points,
                    "earnings": earning_info,
                }
            else:
                # Fallback when no points configuration exists
                try:
                    # Still try to get basic earning info
                    total_earnings_data = EarningCalculationService.get_contributor_total_earnings(
                        contributor
                    )
                    earning_info = {
                        'total_lifetime_earnings': total_earnings_data,
                        'current_month_earnings': None,
                        'points_configuration': None,
                        'error': 'No points configuration found'
                    }
                except Exception as e:
                    earning_info = {
                        'error': f"Error getting earnings: {str(e)}",
                        'total_lifetime_earnings': None,
                        'current_month_earnings': None,
                        'points_configuration': None,
                    }

                response_data = {
                    "contributor": contributor.user.username,
                    "questions_summary": data_summary,
                    "current_month_data": current_month_data,
                    "previous_month_data": previous_month_data,
                    "third_month_data": third_month_data,
                    "earnings": earning_info,
                }

            return Response(response_data, status=HTTP_200_OK)

        except ContributorProfile.DoesNotExist:
            return Response(
                {"error": "Contributor profile not found."},
                status=HTTP_400_BAD_REQUEST,
            )
        except Exception as e:
            return Response(
                {"error": f"An unexpected error occurred: {str(e)}"},
                status=HTTP_400_BAD_REQUEST,
            )
        


class BannerListCreateView(generics.ListCreateAPIView):
    queryset = Banner.objects.all().order_by("-created_at")
    serializer_class = BannerSerializer
    permission_classes = [IsCustomerCareOrReadOnly]
class BannerRetrieveUpdateDestroyView(generics.RetrieveUpdateDestroyAPIView):
    queryset = Banner.objects.all().order_by("-created_at")
    serializer_class = BannerSerializer
    permission_classes = [IsCustomerCareOrReadOnly]


@api_view(["POST"])
def PageVisitors(request):
    if request.method == 'POST':
        data = json.loads(request.body) 
        for key, value in data.items():
            if key == "/":
                key = "/home"
            try:
                value = int(value)
            except ValueError:
                continue  # Skip invalid
            PageCounter.objects.create(url=key, count=value)
        return Response({"msg": "Data received successfully."})


@api_view(['GET'])
def get_all_model_counts(request):
    model_counts = {}

    # Django me registered sare models ke liye loop chalega
    for content_type in ContentType.objects.all():
        model_class = content_type.model_class()
        if model_class:  # Check if model exists
            try:
                # Use uppercase model class name instead of lowercase content_type.model
                model_counts[model_class.__name__] = model_class.objects.count()
            except Exception as e:
                # Handle database errors gracefully (e.g., missing tables)
                print(f"Could not count {model_class.__name__}: {e}")
                model_counts[model_class.__name__] = 0

    return Response(model_counts)


# ============================================================================
# POPUP BANNER VIEWS
# ============================================================================

class ContributorPopupBannerListCreateView(generics.ListCreateAPIView):
    """
    View for contributors to create and list their own popup banners
    """
    serializer_class = PopupBannerContributorSerializer
    permission_classes = [IsContributorForPopupBanner]

    def get_queryset(self):
        """Return only banners created by the current contributor"""
        try:
            return PopupBanner.objects.filter(created_by=self.request.user).order_by('-created_at')
        except Exception as e:
            from log_admin.utils import LoggingUtils
            LoggingUtils.log_error_manual(
                error_type='DATABASE',
                error_message=f"Error fetching popup banners: {str(e)}",
                view_name='ContributorPopupBannerListCreateView.get_queryset',
                severity='MEDIUM',
                user=self.request.user,
                request=self.request
            )
            return PopupBanner.objects.none()

    def perform_create(self, serializer):
        """Set the created_by field to the current user"""
        try:
            serializer.save(created_by=self.request.user)
        except Exception as e:
            from log_admin.utils import LoggingUtils
            LoggingUtils.log_error_manual(
                error_type='VALIDATION',
                error_message=f"Error creating popup banner: {str(e)}",
                view_name='ContributorPopupBannerListCreateView.perform_create',
                severity='HIGH',
                user=self.request.user,
                request=self.request,
                additional_data={'serializer_data': serializer.validated_data if hasattr(serializer, 'validated_data') else {}}
            )
            raise


class ContributorPopupBannerDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    View for contributors to retrieve, update, or delete their own popup banners
    """
    serializer_class = PopupBannerContributorSerializer
    permission_classes = [IsContributorForPopupBanner]

    def get_queryset(self):
        """Return only banners created by the current contributor"""
        try:
            return PopupBanner.objects.filter(created_by=self.request.user)
        except Exception as e:
            from log_admin.utils import LoggingUtils
            LoggingUtils.log_error_manual(
                error_type='DATABASE',
                error_message=f"Error fetching popup banner details: {str(e)}",
                view_name='ContributorPopupBannerDetailView.get_queryset',
                severity='MEDIUM',
                user=self.request.user,
                request=self.request
            )
            return PopupBanner.objects.none()

    def perform_update(self, serializer):
        """Update banner with error logging"""
        try:
            serializer.save()
        except Exception as e:
            from log_admin.utils import LoggingUtils
            LoggingUtils.log_error_manual(
                error_type='VALIDATION',
                error_message=f"Error updating popup banner: {str(e)}",
                view_name='ContributorPopupBannerDetailView.perform_update',
                severity='HIGH',
                user=self.request.user,
                request=self.request,
                additional_data={'banner_id': self.get_object().id if self.get_object() else None}
            )
            raise

    def perform_destroy(self, instance):
        """Delete banner with error logging"""
        try:
            instance.delete()
        except Exception as e:
            from log_admin.utils import LoggingUtils
            LoggingUtils.log_error_manual(
                error_type='DATABASE',
                error_message=f"Error deleting popup banner: {str(e)}",
                view_name='ContributorPopupBannerDetailView.perform_destroy',
                severity='HIGH',
                user=self.request.user,
                request=self.request,
                additional_data={'banner_id': instance.id}
            )
            raise

    def destroy(self, request, *args, **kwargs):
        """Delete banner with success message"""
        instance = self.get_object()
        banner_title = instance.title
        banner_id = instance.id

        # Perform the deletion
        self.perform_destroy(instance)

        return Response({
            'success': True,
            'message': f'Banner "{banner_title}" (ID: {banner_id}) has been successfully deleted.',
            'deleted_banner': {
                'id': banner_id,
                'title': banner_title
            }
        }, status=status.HTTP_200_OK)


class CustomerCarePopupBannerListView(generics.ListAPIView):
    """
    View for customer care to list all popup banners
    """
    serializer_class = PopupBannerCustomerCareSerializer
    permission_classes = [IsCustomerCareForPopupBanner]

    def get_queryset(self):
        """Return all banners, with filtering options"""
        queryset = PopupBanner.objects.all().order_by('-created_at')

        # Filter by approval status
        approval_status = self.request.query_params.get('approval_status')
        if approval_status:
            queryset = queryset.filter(approval_status=approval_status)

        # Filter by content type
        content_type = self.request.query_params.get('content_type')
        if content_type:
            queryset = queryset.filter(content_type=content_type)

        # Filter by creator
        created_by = self.request.query_params.get('created_by')
        if created_by:
            queryset = queryset.filter(created_by__username=created_by)

        return queryset


class CustomerCarePopupBannerDetailView(generics.RetrieveUpdateAPIView):
    """
    View for customer care to retrieve and approve/reject popup banners
    """
    queryset = PopupBanner.objects.all()
    serializer_class = PopupBannerCustomerCareSerializer
    permission_classes = [IsCustomerCareForPopupBanner]


class AdminPopupBannerListView(generics.ListAPIView):
    """
    View for admin to list all popup banners with full details
    """
    queryset = PopupBanner.objects.all().order_by('-created_at')
    serializer_class = PopupBannerAdminSerializer
    permission_classes = [IsAdminForPopupBanner]

    def get_queryset(self):
        """Return all banners with filtering options"""
        queryset = PopupBanner.objects.all().order_by('-created_at')

        # Filter by approval status
        approval_status = self.request.query_params.get('approval_status')
        if approval_status:
            queryset = queryset.filter(approval_status=approval_status)

        # Filter by active status
        is_active = self.request.query_params.get('is_active')
        if is_active is not None:
            queryset = queryset.filter(is_active=is_active.lower() == 'true')

        # Filter by content type
        content_type = self.request.query_params.get('content_type')
        if content_type:
            queryset = queryset.filter(content_type=content_type)

        return queryset


class AdminPopupBannerDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    View for admin to retrieve, update, or delete any popup banner
    """
    queryset = PopupBanner.objects.all()
    serializer_class = PopupBannerAdminSerializer
    permission_classes = [IsAdminForPopupBanner]

    def destroy(self, request, *args, **kwargs):
        """Delete banner with success message (Admin)"""
        instance = self.get_object()
        banner_title = instance.title
        banner_id = instance.id
        created_by = instance.created_by.username

        # Perform the deletion
        self.perform_destroy(instance)

        return Response({
            'success': True,
            'message': f'Banner "{banner_title}" (ID: {banner_id}) created by {created_by} has been successfully deleted by admin.',
            'deleted_banner': {
                'id': banner_id,
                'title': banner_title,
                'created_by': created_by
            },
            'deleted_by': request.user.username
        }, status=status.HTTP_200_OK)


class PopupBannerPublicListView(generics.ListAPIView):
    """
    Public view to list only active popup banners
    """
    serializer_class = PopupBannerPublicSerializer
    permission_classes = [IsPublicReadOnly]

    def get_queryset(self):
        """Return only active banners ordered by priority and creation date"""
        return PopupBanner.objects.filter(
            is_active=True,
            approval_status__in=['approved_by_care', 'approved_by_admin']
        ).order_by('-priority', '-created_at')


class PopupBannerStatsView(APIView):
    """
    View to get statistics about popup banners
    """
    permission_classes = [IsContributorOrCustomerCareOrAdmin]

    def get(self, request):
        """Return banner statistics based on user role"""
        user = request.user
        stats = {}

        # Base queryset based on user role
        if user.is_staff or user.is_superuser:
            # Admin sees all banners
            queryset = PopupBanner.objects.all()
            stats['role'] = 'admin'
        elif hasattr(user, 'customrcare_profile'):
            # Customer care sees all banners
            queryset = PopupBanner.objects.all()
            stats['role'] = 'customer_care'
        elif hasattr(user, 'contributor_profile'):
            # Contributors see only their banners
            queryset = PopupBanner.objects.filter(created_by=user)
            stats['role'] = 'contributor'
        else:
            return Response(
                {"error": "User role not recognized"},
                status=status.HTTP_403_FORBIDDEN
            )

        # Calculate statistics
        stats.update({
            'total_banners': queryset.count(),
            'active_banners': queryset.filter(is_active=True).count(),
            'pending_approval': queryset.filter(approval_status='pending').count(),
            'approved_by_care': queryset.filter(approval_status='approved_by_care').count(),
            'approved_by_admin': queryset.filter(approval_status='approved_by_admin').count(),
            'rejected_by_care': queryset.filter(approval_status='rejected_by_care').count(),
            'rejected_by_admin': queryset.filter(approval_status='rejected_by_admin').count(),
        })

        # Content type breakdown
        content_types = {}
        for choice in PopupBanner.CONTENT_TYPE_CHOICES:
            content_types[choice[0]] = queryset.filter(content_type=choice[0]).count()
        stats['content_types'] = content_types

        # Priority breakdown
        priorities = {}
        for choice in PopupBanner.PRIORITY_CHOICES:
            priorities[choice[0]] = queryset.filter(priority=choice[0]).count()
        stats['priorities'] = priorities

        return Response(stats)


# ============================================================================
# EARNING MANAGEMENT API VIEWS
# ============================================================================

class ContributorEarningListView(generics.ListAPIView):
    """
    View to list contributor earnings with filtering and pagination
    """
    serializer_class = ContributorEarningSerializer
    permission_classes = [IsContributorUser]

    def get_queryset(self):
        """Return earnings for the current contributor"""
        contributor = self.request.user.contributor_profile
        queryset = ContributorEarning.objects.filter(contributor=contributor)

        # Filter by period type if provided
        period_type = self.request.query_params.get('period_type')
        if period_type:
            queryset = queryset.filter(period_type=period_type)

        # Filter by payment status if provided
        is_paid = self.request.query_params.get('is_paid')
        if is_paid is not None:
            queryset = queryset.filter(is_paid=is_paid.lower() == 'true')

        return queryset.order_by('-period_start')


class ContributorEarningDetailView(generics.RetrieveAPIView):
    """
    View to get detailed information about a specific earning record
    """
    serializer_class = ContributorEarningSerializer
    permission_classes = [IsContributorUser]

    def get_queryset(self):
        """Return earnings for the current contributor"""
        contributor = self.request.user.contributor_profile
        return ContributorEarning.objects.filter(contributor=contributor)


class ContributorEarningSummaryView(APIView):
    """
    View to get earning summary for different periods
    """
    permission_classes = [IsContributorUser]

    def get(self, request):
        """Return earning summary for the contributor"""
        try:
            contributor = request.user.contributor_profile
            period_type = request.query_params.get('period_type', 'monthly')

            # Validate period type
            valid_periods = ['daily', 'weekly', 'monthly', 'yearly', 'lifetime']
            if period_type not in valid_periods:
                return Response(
                    {"error": f"Invalid period_type. Must be one of: {', '.join(valid_periods)}"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Get earning summary
            summary = EarningCalculationService.get_earnings_summary(contributor, period_type)

            return Response(summary, status=status.HTTP_200_OK)

        except ContributorProfile.DoesNotExist:
            return Response(
                {"error": "Contributor profile not found."},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            return Response(
                {"error": f"An unexpected error occurred: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class ContributorTotalEarningsView(APIView):
    """
    View to get total lifetime earnings for a contributor
    """
    permission_classes = [IsContributorUser]

    def get(self, request):
        """Return total lifetime earnings"""
        try:
            contributor = request.user.contributor_profile
            total_data = EarningCalculationService.get_contributor_total_earnings(contributor)

            return Response(total_data, status=status.HTTP_200_OK)

        except ContributorProfile.DoesNotExist:
            return Response(
                {"error": "Contributor profile not found."},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            return Response(
                {"error": f"An unexpected error occurred: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class RecalculateEarningsView(APIView):
    """
    View to manually recalculate earnings for a specific period
    """
    permission_classes = [IsContributorUser]

    def post(self, request):
        """Recalculate earnings for the specified period"""
        try:
            contributor = request.user.contributor_profile
            period_type = request.data.get('period_type', 'monthly')

            # Validate period type
            valid_periods = ['daily', 'weekly', 'monthly', 'yearly']
            if period_type not in valid_periods:
                return Response(
                    {"error": f"Invalid period_type. Must be one of: {', '.join(valid_periods)}"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Recalculate earnings
            earning = EarningCalculationService.calculate_and_update_earnings(
                contributor, period_type
            )

            serializer = ContributorEarningSerializer(earning)
            return Response({
                "message": "Earnings recalculated successfully",
                "earning": serializer.data
            }, status=status.HTTP_200_OK)

        except ContributorProfile.DoesNotExist:
            return Response(
                {"error": "Contributor profile not found."},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            return Response(
                {"error": f"An unexpected error occurred: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class ContributorPointsConfigView(APIView):
    """
    View to get the points configuration for a contributor
    """
    permission_classes = [IsContributorUser]

    def get(self, request):
        """Return the points configuration for the contributor"""
        try:
            contributor = request.user.contributor_profile
            points_config = contributor.get_points_config()

            if points_config:
                serializer = ContributorPointsSerializer(points_config)
                return Response({
                    "points_configuration": serializer.data,
                    "is_custom": bool(contributor.custom_points),
                    "contributor": contributor.user.username
                }, status=status.HTTP_200_OK)
            else:
                return Response(
                    {"error": "No points configuration found"},
                    status=status.HTTP_404_NOT_FOUND
                )

        except ContributorProfile.DoesNotExist:
            return Response(
                {"error": "Contributor profile not found."},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            return Response(
                {"error": f"An unexpected error occurred: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
