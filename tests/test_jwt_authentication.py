#!/usr/bin/env python3
"""
Comprehensive JWT Authentication Testing Suite
Tests token expiration, refresh, and authentication flow across all platforms
"""

import os
import sys
import django
import json
import time
import requests
from datetime import datetime, timedelta

# Setup Django
sys.path.insert(0, '/Users/<USER>/Documents/code/shash_b')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'shashtrarth.settings')
django.setup()

from django.contrib.auth.models import User
from django.test import TestCase
from rest_framework.test import APIClient
from rest_framework_simplejwt.tokens import RefreshToken
from students.models import Student
from contributor.models import ContributorProfile
from customrcare.models import CustomrcareProfile

class JWTAuthenticationTestSuite:
    def __init__(self):
        self.base_url = 'http://localhost:8000'
        self.test_results = []
        
    def log_test(self, test_name, status, details=None):
        """Log test results"""
        result = {
            'test_name': test_name,
            'status': status,
            'timestamp': datetime.now().isoformat(),
            'details': details
        }
        self.test_results.append(result)
        print(f"[{status}] {test_name}")
        if details:
            print(f"    Details: {details}")
    
    def create_test_users(self):
        """Create test users for all user types"""
        print("\n=== Creating Test Users ===")

        try:
            # Clean up existing test users first
            User.objects.filter(username__in=[
                'teststudent123', 'testcontrib123', 'testcare123'
            ]).delete()

            # Create student user
            student_user = User.objects.create_user(
                username='teststudent123',
                email='<EMAIL>',
                password='TestPass123!'
            )
            Student.objects.create(
                user=student_user,
                phone=f'test{student_user.id}567890',
                role='student'
            )

            # Create contributor user
            contributor_user = User.objects.create_user(
                username='testcontrib123',
                email='<EMAIL>',
                password='TestPass123!'
            )
            ContributorProfile.objects.create(
                user=contributor_user,
                role='contributor'
            )

            # Create customrcare user
            care_user = User.objects.create_user(
                username='testcare123',
                email='<EMAIL>',
                password='TestPass123!'
            )
            CustomrcareProfile.objects.create(
                user=care_user,
                role='customer_care',
                contact=1234567890
            )

            self.log_test("Create Test Users", "PASS", "All test users created successfully")
            return True

        except Exception as e:
            self.log_test("Create Test Users", "FAIL", str(e))
            return False
    
    def test_login_and_token_generation(self):
        """Test login and token generation for all user types"""
        print("\n=== Testing Login and Token Generation ===")
        
        user_types = [
            ('students', {'username': 'teststudent123', 'password': 'TestPass123!'}),
            ('contributor', {'username': 'testcontrib123', 'password': 'TestPass123!'}),
            ('customrcare', {'username': 'testcare123', 'password': 'TestPass123!'})
        ]
        
        tokens = {}
        
        for user_type, credentials in user_types:
            try:
                url = f"{self.base_url}/api/{user_type}/login/"
                response = requests.post(url, json=credentials)
                
                if response.status_code == 200:
                    data = response.json()
                    
                    # Extract tokens based on user type
                    if user_type == 'students':
                        token_data = data.get('JWT_Token', {})
                    else:
                        token_data = {
                            'access': data.get('access'),
                            'refresh': data.get('refresh')
                        }
                    
                    if token_data.get('access') and token_data.get('refresh'):
                        tokens[user_type] = token_data
                        self.log_test(f"Login {user_type}", "PASS", f"Tokens received")
                    else:
                        self.log_test(f"Login {user_type}", "FAIL", "Missing tokens in response")
                else:
                    self.log_test(f"Login {user_type}", "FAIL", f"HTTP {response.status_code}: {response.text}")
                    
            except Exception as e:
                self.log_test(f"Login {user_type}", "FAIL", str(e))
        
        return tokens
    
    def test_token_refresh(self, tokens):
        """Test token refresh functionality"""
        print("\n=== Testing Token Refresh ===")
        
        for user_type, token_data in tokens.items():
            try:
                url = f"{self.base_url}/api/{user_type}/token/refresh/"
                refresh_payload = {'refresh': token_data['refresh']}
                
                response = requests.post(url, json=refresh_payload)
                
                if response.status_code == 200:
                    new_data = response.json()
                    
                    if new_data.get('access'):
                        # Update tokens for further testing
                        tokens[user_type]['access'] = new_data['access']
                        if new_data.get('refresh'):  # Token rotation enabled
                            tokens[user_type]['refresh'] = new_data['refresh']
                        
                        self.log_test(f"Token Refresh {user_type}", "PASS", "New access token received")
                    else:
                        self.log_test(f"Token Refresh {user_type}", "FAIL", "No access token in response")
                else:
                    self.log_test(f"Token Refresh {user_type}", "FAIL", f"HTTP {response.status_code}: {response.text}")
                    
            except Exception as e:
                self.log_test(f"Token Refresh {user_type}", "FAIL", str(e))
    
    def test_authenticated_requests(self, tokens):
        """Test authenticated API requests"""
        print("\n=== Testing Authenticated Requests ===")
        
        # Test endpoints for each user type
        test_endpoints = {
            'students': '/api/students/list/',
            'contributor': '/api/contributor/dashboard/',
            'customrcare': '/api/customrcare/questions/'
        }
        
        for user_type, token_data in tokens.items():
            endpoint = test_endpoints.get(user_type)
            if not endpoint:
                continue
                
            try:
                url = f"{self.base_url}{endpoint}"
                headers = {'Authorization': f"Bearer {token_data['access']}"}
                
                response = requests.get(url, headers=headers)
                
                if response.status_code in [200, 201]:
                    self.log_test(f"Authenticated Request {user_type}", "PASS", f"Endpoint accessible")
                elif response.status_code == 401:
                    self.log_test(f"Authenticated Request {user_type}", "FAIL", "Authentication failed")
                else:
                    self.log_test(f"Authenticated Request {user_type}", "WARN", f"HTTP {response.status_code}")
                    
            except Exception as e:
                self.log_test(f"Authenticated Request {user_type}", "FAIL", str(e))
    
    def test_invalid_token_handling(self, tokens):
        """Test handling of invalid tokens"""
        print("\n=== Testing Invalid Token Handling ===")
        
        for user_type in tokens.keys():
            try:
                url = f"{self.base_url}/api/{user_type}/token/refresh/"
                invalid_payload = {'refresh': 'invalid_token_12345'}
                
                response = requests.post(url, json=invalid_payload)
                
                if response.status_code == 401:
                    data = response.json()
                    if data.get('error') and data.get('code'):
                        self.log_test(f"Invalid Token {user_type}", "PASS", "Proper error response")
                    else:
                        self.log_test(f"Invalid Token {user_type}", "WARN", "Missing error details")
                else:
                    self.log_test(f"Invalid Token {user_type}", "FAIL", f"Expected 401, got {response.status_code}")
                    
            except Exception as e:
                self.log_test(f"Invalid Token {user_type}", "FAIL", str(e))
    
    def test_token_expiration_simulation(self):
        """Test token expiration by creating short-lived tokens"""
        print("\n=== Testing Token Expiration Simulation ===")
        
        try:
            # Create a user and generate a token with very short expiry
            from django.conf import settings
            from rest_framework_simplejwt.tokens import AccessToken
            
            # Temporarily modify token lifetime for testing
            original_lifetime = settings.SIMPLE_JWT['ACCESS_TOKEN_LIFETIME']
            settings.SIMPLE_JWT['ACCESS_TOKEN_LIFETIME'] = timedelta(seconds=1)
            
            # Create token
            user = User.objects.get(username='teststudent123')
            token = AccessToken.for_user(user)
            
            # Wait for token to expire
            time.sleep(2)
            
            # Test expired token
            url = f"{self.base_url}/api/students/list/"
            headers = {'Authorization': f"Bearer {str(token)}"}
            response = requests.get(url, headers=headers)
            
            # Restore original lifetime
            settings.SIMPLE_JWT['ACCESS_TOKEN_LIFETIME'] = original_lifetime
            
            if response.status_code == 401:
                self.log_test("Token Expiration", "PASS", "Expired token properly rejected")
            else:
                self.log_test("Token Expiration", "FAIL", f"Expected 401, got {response.status_code}")
                
        except Exception as e:
            self.log_test("Token Expiration", "FAIL", str(e))
    
    def generate_curl_examples(self, tokens):
        """Generate curl command examples for testing"""
        print("\n=== Generating Curl Examples ===")
        
        curl_examples = []
        
        for user_type, token_data in tokens.items():
            # Login example
            login_curl = f"""
# Login for {user_type}
curl -X POST {self.base_url}/api/{user_type}/login/ \\
  -H "Content-Type: application/json" \\
  -d '{{"username": "test{user_type}123", "password": "TestPass123!"}}'
"""
            curl_examples.append(login_curl)
            
            # Token refresh example
            refresh_curl = f"""
# Token refresh for {user_type}
curl -X POST {self.base_url}/api/{user_type}/token/refresh/ \\
  -H "Content-Type: application/json" \\
  -d '{{"refresh": "{token_data['refresh']}"}}'
"""
            curl_examples.append(refresh_curl)
            
            # Authenticated request example
            auth_curl = f"""
# Authenticated request for {user_type}
curl -X GET {self.base_url}/api/{user_type}/dashboard/ \\
  -H "Authorization: Bearer {token_data['access']}"
"""
            curl_examples.append(auth_curl)
        
        # Save curl examples to file
        with open('tests/jwt_curl_examples.md', 'w') as f:
            f.write("# JWT Authentication Curl Examples\n\n")
            f.write("Generated on: " + datetime.now().isoformat() + "\n\n")
            for example in curl_examples:
                f.write(example + "\n")
        
        self.log_test("Generate Curl Examples", "PASS", "Examples saved to jwt_curl_examples.md")
    
    def cleanup_test_users(self):
        """Clean up test users"""
        print("\n=== Cleaning Up Test Users ===")
        
        try:
            User.objects.filter(username__in=[
                'teststudent123', 'testcontrib123', 'testcare123'
            ]).delete()
            
            self.log_test("Cleanup Test Users", "PASS", "Test users removed")
            
        except Exception as e:
            self.log_test("Cleanup Test Users", "FAIL", str(e))
    
    def run_all_tests(self):
        """Run all JWT authentication tests"""
        print("Starting JWT Authentication Test Suite")
        print("=" * 50)
        
        # Create test users
        if not self.create_test_users():
            print("Failed to create test users. Aborting tests.")
            return
        
        try:
            # Test login and token generation
            tokens = self.test_login_and_token_generation()
            
            if tokens:
                # Test token refresh
                self.test_token_refresh(tokens)
                
                # Test authenticated requests
                self.test_authenticated_requests(tokens)
                
                # Test invalid token handling
                self.test_invalid_token_handling(tokens)
                
                # Generate curl examples
                self.generate_curl_examples(tokens)
            
            # Test token expiration
            self.test_token_expiration_simulation()
            
        finally:
            # Cleanup
            self.cleanup_test_users()
        
        # Print summary
        self.print_test_summary()
    
    def print_test_summary(self):
        """Print test results summary"""
        print("\n" + "=" * 50)
        print("TEST SUMMARY")
        print("=" * 50)
        
        passed = len([r for r in self.test_results if r['status'] == 'PASS'])
        failed = len([r for r in self.test_results if r['status'] == 'FAIL'])
        warnings = len([r for r in self.test_results if r['status'] == 'WARN'])
        
        print(f"Total Tests: {len(self.test_results)}")
        print(f"Passed: {passed}")
        print(f"Failed: {failed}")
        print(f"Warnings: {warnings}")
        
        if failed > 0:
            print("\nFailed Tests:")
            for result in self.test_results:
                if result['status'] == 'FAIL':
                    print(f"  - {result['test_name']}: {result['details']}")
        
        # Save detailed results
        with open('tests/jwt_test_results.json', 'w') as f:
            json.dump(self.test_results, f, indent=2)
        
        print(f"\nDetailed results saved to jwt_test_results.json")

if __name__ == '__main__':
    test_suite = JWTAuthenticationTestSuite()
    test_suite.run_all_tests()
