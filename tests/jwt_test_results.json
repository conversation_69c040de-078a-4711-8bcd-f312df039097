[{"test_name": "Create Test Users", "status": "PASS", "timestamp": "2025-07-28T17:17:33.324798", "details": "All test users created successfully"}, {"test_name": "Login students", "status": "PASS", "timestamp": "2025-07-28T17:17:33.659790", "details": "Token<PERSON> received"}, {"test_name": "Login contributor", "status": "PASS", "timestamp": "2025-07-28T17:17:33.956563", "details": "Token<PERSON> received"}, {"test_name": "Login customrcare", "status": "FAIL", "timestamp": "2025-07-28T17:17:34.250223", "details": "HTTP 401: {\"non_field_errors\":[\"Access denied. Invalid role for customer care access.\"]}"}, {"test_name": "Token Refresh students", "status": "PASS", "timestamp": "2025-07-28T17:17:34.262915", "details": "New access token received"}, {"test_name": "Token Refresh contributor", "status": "PASS", "timestamp": "2025-07-28T17:17:34.273595", "details": "New access token received"}, {"test_name": "Authenticated Request students", "status": "PASS", "timestamp": "2025-07-28T17:17:34.301079", "details": "Endpoint accessible"}, {"test_name": "Authenticated Request contributor", "status": "PASS", "timestamp": "2025-07-28T17:17:34.361331", "details": "Endpoint accessible"}, {"test_name": "Invalid Token students", "status": "PASS", "timestamp": "2025-07-28T17:17:34.370938", "details": "Proper error response"}, {"test_name": "<PERSON><PERSON><PERSON> contributor", "status": "PASS", "timestamp": "2025-07-28T17:17:34.379887", "details": "Proper error response"}, {"test_name": "Generate Curl Examples", "status": "PASS", "timestamp": "2025-07-28T17:17:34.380470", "details": "Examples saved to jwt_curl_examples.md"}, {"test_name": "Token Expiration", "status": "FAIL", "timestamp": "2025-07-28T17:17:36.421413", "details": "Expected 401, got 200"}, {"test_name": "Cleanup Test Users", "status": "PASS", "timestamp": "2025-07-28T17:17:36.438203", "details": "Test users removed"}]