#!/usr/bin/env python3
"""
Comprehensive test suite for Walk-Around Images API
Tests all CRUD operations, authentication, and business logic
"""

import os
import sys
import django
import json
import tempfile
from PIL import Image
import io

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'shashtrarth.settings')
sys.path.append('/Users/<USER>/Documents/code/shash_b')

django.setup()

from django.test import TestCase, Client
from django.contrib.auth.models import User
from django.core.files.uploadedfile import SimpleUploadedFile
from rest_framework.test import APIClient
from rest_framework import status
from customrcare.models import CustomrcareProfile, WalkAroundImage
from students.models import Student


def create_test_image(name='test_image.jpg', format='JPEG', size=(100, 100)):
    """Create a test image file."""
    image = Image.new('RGB', size, color='red')
    image_file = io.BytesIO()
    image.save(image_file, format=format)
    image_file.seek(0)
    return SimpleUploadedFile(
        name=name,
        content=image_file.getvalue(),
        content_type=f'image/{format.lower()}'
    )


class WalkAroundImagesAPITest:
    """Test suite for Walk-Around Images API"""
    
    def __init__(self):
        self.client = APIClient()
        self.setup_test_data()
    
    def setup_test_data(self):
        """Create test users and profiles"""
        print("Setting up test data...")

        import random
        import string

        # Generate unique usernames
        suffix = ''.join(random.choices(string.ascii_lowercase + string.digits, k=6))

        # Create customer care user
        care_username = f'testcare_{suffix}'
        self.care_user, created = User.objects.get_or_create(
            username=care_username,
            defaults={
                'email': f'testcare_{suffix}@example.com',
                'password': 'testpass123'
            }
        )
        if created:
            self.care_user.set_password('testpass123')
            self.care_user.save()

        self.care_profile, created = CustomrcareProfile.objects.get_or_create(
            user=self.care_user,
            defaults={
                'role': 'customrcare',
                'contact': 1234567890
            }
        )

        # Create student user
        student_username = f'teststudent_{suffix}'
        self.student_user, created = User.objects.get_or_create(
            username=student_username,
            defaults={
                'email': f'teststudent_{suffix}@example.com',
                'password': 'testpass123'
            }
        )
        if created:
            self.student_user.set_password('testpass123')
            self.student_user.save()

        self.student_profile, created = Student.objects.get_or_create(
            user=self.student_user,
            defaults={
                'phone': f'123456{suffix[:4]}',
                'role': 'student'
            }
        )

        # Create unauthorized user
        unauth_username = f'unauthorized_{suffix}'
        self.unauthorized_user, created = User.objects.get_or_create(
            username=unauth_username,
            defaults={
                'email': f'unauthorized_{suffix}@example.com',
                'password': 'testpass123'
            }
        )
        if created:
            self.unauthorized_user.set_password('testpass123')
            self.unauthorized_user.save()

        print("✓ Test data setup complete")
    
    def authenticate_user(self, user):
        """Authenticate a user and return token"""
        if hasattr(user, 'customrcare_profile'):
            response = self.client.post('/api/customrcare/login/', {
                'username': user.username,
                'password': 'testpass123'
            })
        else:
            response = self.client.post('/api/students/login/', {
                'username': user.username,
                'password': 'testpass123'
            })

        print(f"Login response status: {response.status_code}")
        print(f"Login response data: {response.data}")

        if response.status_code == 200:
            # Try different possible token field names
            token = None
            if 'JWT_Token' in response.data:
                token = response.data['JWT_Token']['access']
            elif 'access' in response.data:
                token = response.data['access']
            elif 'token' in response.data:
                token = response.data['token']

            if token:
                self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
                return token
        return None
    
    def test_public_access(self):
        """Test that endpoints are publicly accessible"""
        print("\n🌐 Testing public access...")

        # Clear any existing authentication
        self.client.credentials()

        # Test GET endpoints without authentication
        get_endpoints = [
            ('GET', '/api/customrcare/walk-around-images/'),
            ('GET', '/api/customrcare/walk-around-images/stats/'),
        ]

        for method, url in get_endpoints:
            response = getattr(self.client, method.lower())(url)
            assert response.status_code == 200, f"Expected 200 for {method} {url}, got {response.status_code}"

        print("✓ All endpoints are publicly accessible")
    
    def test_create_walk_around_image(self):
        """Test creating walk-around images"""
        print("\n📷 Testing image creation...")
        
        # Test with customer care user
        self.authenticate_user(self.care_user)
        
        test_image = create_test_image('test1.jpg')
        data = {
            'image': test_image,
            'title': 'Test Walk Around Image',
            'description': 'This is a test image',
            'status': 'active'
        }
        
        response = self.client.post('/api/customrcare/walk-around-images/', data, format='multipart')
        print(f"Create response status: {response.status_code}")
        print(f"Create response data: {response.data}")
        
        assert response.status_code == 201, f"Expected 201, got {response.status_code}"
        assert 'id' in response.data
        assert response.data['title'] == 'Test Walk Around Image'
        assert response.data['status'] == 'active'
        
        print("✓ Image creation successful")
        return response.data['id']

    def test_anonymous_image_creation(self):
        """Test creating walk-around images without authentication"""
        print("\n👤 Testing anonymous image creation...")

        # Clear authentication
        self.client.credentials()

        test_image = create_test_image('anonymous_test.jpg')
        data = {
            'image': test_image,
            'title': 'Anonymous Walk Around Image',
            'description': 'This is an anonymous test image',
            'status': 'active'
        }

        response = self.client.post('/api/customrcare/walk-around-images/', data, format='multipart')
        print(f"Anonymous create response status: {response.status_code}")
        print(f"Anonymous create response data: {response.data}")

        assert response.status_code == 201, f"Expected 201, got {response.status_code}"
        assert 'id' in response.data
        assert response.data['title'] == 'Anonymous Walk Around Image'
        assert response.data['status'] == 'active'
        assert response.data['user_username'] == 'Anonymous'

        print("✓ Anonymous image creation successful")
    
    def test_list_walk_around_images(self):
        """Test listing walk-around images"""
        print("\n📋 Testing image listing...")
        
        self.authenticate_user(self.care_user)
        
        # Create a few test images
        for i in range(3):
            test_image = create_test_image(f'test{i}.jpg')
            data = {
                'image': test_image,
                'title': f'Test Image {i}',
                'status': 'active'
            }
            self.client.post('/api/customrcare/walk-around-images/', data, format='multipart')
        
        # List images
        response = self.client.get('/api/customrcare/walk-around-images/')
        print(f"List response status: {response.status_code}")
        
        assert response.status_code == 200

        # Handle both paginated and non-paginated responses
        if hasattr(response.data, 'get') and 'results' in response.data:
            images = response.data['results']
        else:
            images = response.data

        assert len(images) >= 3
        
        print(f"✓ Listed {len(images)} images successfully")
    
    def test_update_walk_around_image(self):
        """Test updating walk-around images"""
        print("\n✏️ Testing image updates...")
        
        self.authenticate_user(self.care_user)
        
        # Create an image first
        image_id = self.test_create_walk_around_image()
        
        # Update the image
        update_data = {
            'title': 'Updated Title',
            'description': 'Updated description',
            'status': 'inactive'
        }
        
        response = self.client.patch(f'/api/customrcare/walk-around-images/{image_id}/', update_data)
        print(f"Update response status: {response.status_code}")
        
        assert response.status_code == 200
        assert response.data['title'] == 'Updated Title'
        assert response.data['status'] == 'inactive'
        
        print("✓ Image update successful")
    
    def test_status_update_endpoint(self):
        """Test the dedicated status update endpoint"""
        print("\n🔄 Testing status update endpoint...")
        
        self.authenticate_user(self.care_user)
        
        # Create an image first
        image_id = self.test_create_walk_around_image()
        
        # Update status to inactive
        response = self.client.patch(f'/api/customrcare/walk-around-images/{image_id}/status/', {
            'status': 'inactive'
        })
        
        assert response.status_code == 200
        assert response.data['status'] == 'inactive'
        
        print("✓ Status update successful")
    
    def test_active_image_limit(self):
        """Test the 5 active images limit (global limit)"""
        print("\n🔢 Testing global active image limit (max 5)...")

        # First, deactivate all existing active images to start fresh
        self.client.credentials()  # No auth needed

        # Get all active images and deactivate them
        response = self.client.get('/api/customrcare/walk-around-images/')
        if hasattr(response.data, 'get') and 'results' in response.data:
            images = response.data['results']
        else:
            images = response.data

        for img in images:
            if img['status'] == 'active':
                self.client.patch(f'/api/customrcare/walk-around-images/{img["id"]}/status/', {
                    'status': 'inactive'
                })

        # Now create 6 active images to test the limit
        image_ids = []
        for i in range(6):
            test_image = create_test_image(f'global_limit_test_{i}.jpg')
            data = {
                'image': test_image,
                'title': f'Global Limit Test Image {i}',
                'status': 'active'
            }
            response = self.client.post('/api/customrcare/walk-around-images/', data, format='multipart')
            assert response.status_code == 201
            image_ids.append(response.data['id'])

        # Check that only 5 are active globally
        response = self.client.get('/api/customrcare/walk-around-images/')

        # Handle both paginated and non-paginated responses
        if hasattr(response.data, 'get') and 'results' in response.data:
            images = response.data['results']
        else:
            images = response.data

        active_images = [img for img in images if img['status'] == 'active']

        assert len(active_images) == 5, f"Expected 5 active images globally, got {len(active_images)}"

        print("✓ Global active image limit enforced correctly")
    
    def test_stats_endpoint(self):
        """Test the statistics endpoint"""
        print("\n📊 Testing statistics endpoint...")
        
        self.authenticate_user(self.care_user)
        
        response = self.client.get('/api/customrcare/walk-around-images/stats/')
        print(f"Stats response: {response.data}")
        
        assert response.status_code == 200
        assert 'total_images' in response.data
        assert 'active_images' in response.data
        assert 'inactive_images' in response.data
        assert 'max_active_allowed' in response.data
        assert response.data['max_active_allowed'] == 5
        
        print("✓ Statistics endpoint working correctly")
    
    def test_delete_walk_around_image(self):
        """Test deleting walk-around images with proper message"""
        print("\n🗑️ Testing image deletion...")

        self.authenticate_user(self.care_user)

        # Create an image first
        test_image = create_test_image('delete_test.jpg')
        data = {
            'image': test_image,
            'title': 'Image to Delete',
            'status': 'active'
        }
        create_response = self.client.post('/api/customrcare/walk-around-images/', data, format='multipart')
        assert create_response.status_code == 201
        image_id = create_response.data['id']

        # Delete the image
        delete_response = self.client.delete(f'/api/customrcare/walk-around-images/{image_id}/')
        print(f"Delete response status: {delete_response.status_code}")
        print(f"Delete response data: {delete_response.data}")

        assert delete_response.status_code == 200
        assert 'message' in delete_response.data
        assert 'deleted_image_id' in delete_response.data
        assert delete_response.data['deleted_image_id'] == image_id
        assert 'Image to Delete' in delete_response.data['message']

        # Verify image is actually deleted
        get_response = self.client.get(f'/api/customrcare/walk-around-images/{image_id}/')
        assert get_response.status_code == 404

        print("✓ Image deletion with message successful")

    def test_public_visibility(self):
        """Test that all users can see all images (public access)"""
        print("\n🌐 Testing public visibility...")

        # Create image as care user
        self.authenticate_user(self.care_user)
        test_image = create_test_image('care_user_public_image.jpg')
        data = {
            'image': test_image,
            'title': 'Care User Public Image',
            'status': 'active'
        }
        care_response = self.client.post('/api/customrcare/walk-around-images/', data, format='multipart')
        assert care_response.status_code == 201

        # Switch to student user
        self.authenticate_user(self.student_user)

        # Student should be able to see care user's images (public access)
        response = self.client.get('/api/customrcare/walk-around-images/')

        # Handle both paginated and non-paginated responses
        if hasattr(response.data, 'get') and 'results' in response.data:
            images = response.data['results']
        else:
            images = response.data

        # Should contain care user's image
        care_image_titles = [img['title'] for img in images if img['title'] == 'Care User Public Image']
        assert len(care_image_titles) > 0, "All users should be able to see all images (public access)"

        # Test anonymous access too
        self.client.credentials()  # Clear authentication
        response = self.client.get('/api/customrcare/walk-around-images/')

        if hasattr(response.data, 'get') and 'results' in response.data:
            images = response.data['results']
        else:
            images = response.data

        care_image_titles = [img['title'] for img in images if img['title'] == 'Care User Public Image']
        assert len(care_image_titles) > 0, "Anonymous users should also be able to see all images"

        print("✓ Public visibility working correctly")
    
    def run_all_tests(self):
        """Run all tests"""
        print("🚀 Starting Walk-Around Images API Tests")
        print("=" * 50)
        
        try:
            self.test_public_access()
            self.test_create_walk_around_image()
            self.test_anonymous_image_creation()
            self.test_list_walk_around_images()
            self.test_update_walk_around_image()
            self.test_delete_walk_around_image()
            self.test_status_update_endpoint()
            self.test_active_image_limit()
            self.test_stats_endpoint()
            self.test_public_visibility()
            
            print("\n" + "=" * 50)
            print("🎉 All tests passed successfully!")
            print("✅ Walk-Around Images API is working correctly")
            
        except Exception as e:
            print(f"\n❌ Test failed: {str(e)}")
            import traceback
            traceback.print_exc()
            return False
        
        return True


if __name__ == '__main__':
    tester = WalkAroundImagesAPITest()
    success = tester.run_all_tests()
    sys.exit(0 if success else 1)
