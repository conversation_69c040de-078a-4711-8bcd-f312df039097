# JWT Authentication Curl Examples

Generated on: 2025-07-28T17:17:34.380388


# Login for students
curl -X POST http://localhost:8000/api/students/login/ \
  -H "Content-Type: application/json" \
  -d '{"username": "teststudents123", "password": "TestPass123!"}'


# Token refresh for students
curl -X POST http://localhost:8000/api/students/token/refresh/ \
  -H "Content-Type: application/json" \
  -d '{"refresh": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc1NDMwODA1MywiaWF0IjoxNzUzNzAzMjUzLCJqdGkiOiIyNDIwMDU5NGM4MWU0ZWQ1ODc4NzdmOTQ3NmY4NzAxNyIsInVzZXJfaWQiOjI0Mn0.fnh028NVG7-GSiDLZbHUXnSJVcNXNEVu-MWEsHcC6A4"}'


# Authenticated request for students
curl -X GET http://localhost:8000/api/students/dashboard/ \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzUzNzA0MTU0LCJpYXQiOjE3NTM3MDMyNTMsImp0aSI6IjBiOTVjZTE5YmFiNzQyYWRhNGY0Mzc3NWFmMWQwNWIwIiwidXNlcl9pZCI6MjQyfQ.I_Vulc1-R6_VnLZc1U_VrwohAuHGg1qxXbzukinN2Cg"


# Login for contributor
curl -X POST http://localhost:8000/api/contributor/login/ \
  -H "Content-Type: application/json" \
  -d '{"username": "testcontributor123", "password": "TestPass123!"}'


# Token refresh for contributor
curl -X POST http://localhost:8000/api/contributor/token/refresh/ \
  -H "Content-Type: application/json" \
  -d '{"refresh": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc1NDMwODA1MywiaWF0IjoxNzUzNzAzMjUzLCJqdGkiOiJiOWM0ZDE3MjVlNGU0ZDU0YTNmZmMzYTk1MTQzN2I3MSIsInVzZXJfaWQiOjI0M30.SIZKuUpypO8MDmIMt5YYBFbhxy_J_x3XUEPdVVKdDiU"}'


# Authenticated request for contributor
curl -X GET http://localhost:8000/api/contributor/dashboard/ \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzUzNzA0MTU0LCJpYXQiOjE3NTM3MDMyNTMsImp0aSI6IjA1ZTYxYjViODgwMjRkNmRhOTk2ZjM3ZDExZGNmMjM5IiwidXNlcl9pZCI6MjQzfQ.4iqUBTxfec7_-Fu0GVjrjdyty1ehYD3MH19naj51k74"

