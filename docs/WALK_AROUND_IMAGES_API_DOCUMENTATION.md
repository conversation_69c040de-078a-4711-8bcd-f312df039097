# Walk-Around Images API Documentation

## Overview
The Walk-Around Images API is **publicly accessible** and allows anyone to manage walk-around images with the following features:
- Add, view, edit, and remove walk-around images
- Status management (active/inactive)
- Maximum 5 active images globally (automatic deactivation of oldest when limit exceeded)
- Support for both authenticated and anonymous users
- Public access - no authentication required

## Authentication
**No authentication required!** All endpoints are publicly accessible.
- Authenticated users: Images are associated with their user account
- Anonymous users: Images are created with `user: null` and `user_username: "Anonymous"`

## Base URL
```
http://localhost:8000/api/customrcare/
```

## Endpoints

### 1. List and Create Walk-Around Images
**Endpoint:** `GET/POST /walk-around-images/`

#### GET - List Images
Lists all walk-around images for the authenticated user.

**Request:**
```http
GET /api/customrcare/walk-around-images/
Authorization: Bearer <token>
```

**Response (200 OK):**
```json
{
  "count": 3,
  "next": null,
  "previous": null,
  "results": [
    {
      "id": 1,
      "user": 1,
      "user_username": "testuser",
      "image": "/media/walk_around_images/image1.jpg",
      "image_url": "http://localhost:8000/media/walk_around_images/image1.jpg",
      "title": "Front View",
      "description": "Front view of the vehicle",
      "status": "active",
      "created_at": "2024-01-15T10:30:00Z",
      "updated_at": "2024-01-15T10:30:00Z"
    }
  ]
}
```

#### POST - Create Image
Creates a new walk-around image. No authentication required.

**Request:**
```http
POST /api/customrcare/walk-around-images/
Content-Type: multipart/form-data

Form Data:
- image: [image file]
- title: "Front View"
- description: "  "
- status: "active"
```

**Response (201 Created) - Authenticated User:**
```json
{
  "id": 1,
  "user": 1,
  "user_username": "testuser",
  "image": "/media/walk_around_images/image1.jpg",
  "image_url": "http://localhost:8000/media/walk_around_images/image1.jpg",
  "title": "Front View",
  "description": "Front view of the vehicle",
  "status": "active",
  "created_at": "2024-01-15T10:30:00Z",
  "updated_at": "2024-01-15T10:30:00Z"
}
```

**Response (201 Created) - Anonymous User:**
```json
{
  "id": 2,
  "user": null,
  "user_username": "Anonymous",
  "image": "/media/walk_around_images/image2.jpg",
  "image_url": "http://localhost:8000/media/walk_around_images/image2.jpg",
  "title": "Front View",
  "description": "Front view of the vehicle",
  "status": "active",
  "created_at": "2024-01-15T10:35:00Z",
  "updated_at": "2024-01-15T10:35:00Z"
}
```

### 2. Retrieve, Update, Delete Specific Image
**Endpoint:** `GET/PUT/PATCH/DELETE /walk-around-images/{id}/`

#### GET - Retrieve Image
**Request:**
```http
GET /api/customrcare/walk-around-images/1/
Authorization: Bearer <token>
```

#### PUT/PATCH - Update Image
**Request:**
```http
PATCH /api/customrcare/walk-around-images/1/
Authorization: Bearer <token>
Content-Type: application/json

{
  "title": "Updated Front View",
  "description": "Updated description",
  "status": "inactive"
}
```

#### DELETE - Remove Image
**Request:**
```http
DELETE /api/customrcare/walk-around-images/1/
Authorization: Bearer <token>
```

**Response (200 OK):**
```json
{
  "message": "Walk-around image \"Front View\" (ID: 1) has been successfully deleted.",
  "deleted_image_id": 1,
  "deleted_image_title": "Front View"
}
```

### 3. Update Image Status
**Endpoint:** `PATCH /walk-around-images/{id}/status/`

Dedicated endpoint for updating only the status of an image.

**Request:**
```http
PATCH /api/customrcare/walk-around-images/1/status/
Authorization: Bearer <token>
Content-Type: application/json

{
  "status": "inactive"
}
```

**Response (200 OK):**
```json
{
  "id": 1,
  "user": 1,
  "user_username": "testuser",
  "image": "/media/walk_around_images/image1.jpg",
  "image_url": "http://localhost:8000/media/walk_around_images/image1.jpg",
  "title": "Front View",
  "description": "Front view of the vehicle",
  "status": "inactive",
  "created_at": "2024-01-15T10:30:00Z",
  "updated_at": "2024-01-15T10:35:00Z"
}
```

### 4. Get Statistics
**Endpoint:** `GET /walk-around-images/stats/`

Returns statistics about the user's walk-around images.

**Request:**
```http
GET /api/customrcare/walk-around-images/stats/
Authorization: Bearer <token>
```

**Response (200 OK):**
```json
{
  "total_images": 7,
  "active_images": 5,
  "inactive_images": 2,
  "max_active_allowed": 5,
  "can_add_more_active": false
}
```

## Business Rules

### Active Image Limit
- Maximum 5 active images **globally** (across all users)
- When creating/updating an image to active status and the limit is exceeded:
  - The oldest active image (globally) is automatically set to inactive
  - The new/updated image becomes active

### Status Values
- `active`: Image is currently active and counts toward the 5-image global limit
- `inactive`: Image is deactivated and doesn't count toward the limit

### User Access
- **Public Access**: Anyone can view, create, update, and delete any image
- **No Authentication Required**: All operations are publicly accessible
- **User Association**: Images can be associated with authenticated users or be anonymous

## Error Responses

### 401 Unauthorized
```json
{
  "detail": "Authentication credentials were not provided."
}
```

### 403 Forbidden
```json
{
  "detail": "You do not have permission for this resource."
}
```

### 400 Bad Request
```json
{
  "status": ["Status must be either 'active' or 'inactive'."]
}
```

### 404 Not Found
```json
{
  "detail": "Not found."
}
```

## Testing with cURL

### 1. Create walk-around image (No authentication required)
```bash
curl -X POST http://localhost:8000/api/customrcare/walk-around-images/ \
  -F "image=@/path/to/your/image.jpg" \
  -F "title=Front View" \
  -F "description=Front view of vehicle" \
  -F "status=active"
```

### 2. List images (No authentication required)
```bash
curl -X GET http://localhost:8000/api/customrcare/walk-around-images/
```

### 3. Update image status (No authentication required)
```bash
curl -X PATCH http://localhost:8000/api/customrcare/walk-around-images/1/status/ \
  -H "Content-Type: application/json" \
  -d '{"status": "inactive"}'
```

### 4. Delete image (No authentication required)
```bash
curl -X DELETE http://localhost:8000/api/customrcare/walk-around-images/1/
```

### 5. Get statistics (No authentication required)
```bash
curl -X GET http://localhost:8000/api/customrcare/walk-around-images/stats/
```

### 6. Create authenticated image (Optional - if you want user association)
```bash
# First login to get token
curl -X POST http://localhost:8000/api/customrcare/login/ \
  -H "Content-Type: application/json" \
  -d '{"username": "your_username", "password": "your_password"}'

# Then create image with authentication
curl -X POST http://localhost:8000/api/customrcare/walk-around-images/ \
  -H "Authorization: Bearer <your_token>" \
  -F "image=@/path/to/your/image.jpg" \
  -F "title=Front View" \
  -F "description=Front view of vehicle" \
  -F "status=active"
```

## Notes
- All image uploads are stored in the `media/walk_around_images/` directory
- Image URLs are returned as absolute URLs in API responses
- The API supports common image formats (JPEG, PNG, etc.)
- File size limits apply as configured in Django settings
