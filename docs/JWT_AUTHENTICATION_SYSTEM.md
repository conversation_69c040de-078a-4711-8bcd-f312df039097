# JWT Authentication System Documentation

## Overview

This document describes the comprehensive JWT (JSON Web Token) authentication system implemented across Django backend, React frontend, and React Native mobile applications. The system provides secure, automatic token refresh capabilities with short-lived access tokens and long-lived refresh tokens.

## Key Features

- **Short-lived Access Tokens**: 15-minute expiry for enhanced security
- **Long-lived Refresh Tokens**: 7-day expiry for user convenience
- **Automatic Token Rotation**: Enhanced security through token rotation
- **Cross-Platform Support**: Django, React, and React Native
- **Automatic Token Refresh**: Seamless token refresh without user intervention
- **Secure Storage**: Keychain storage for React Native, localStorage for React
- **Comprehensive Error Handling**: Detailed error codes and messages

## Architecture

### Token Lifecycle

1. **Login**: User provides credentials, receives access + refresh tokens
2. **API Requests**: Access token used for authentication
3. **Token Expiry**: When access token expires (15 min), automatic refresh occurs
4. **Token Refresh**: Refresh token used to get new access token
5. **Token Rotation**: New refresh token provided (optional, enabled by default)
6. **Logout**: Tokens blacklisted on server and cleared from client

### Security Features

- Access tokens expire quickly (15 minutes) to limit exposure
- Refresh tokens are long-lived (7 days) but can be revoked
- Token rotation prevents replay attacks
- Secure storage prevents token theft
- Comprehensive logging for security monitoring

## Django Backend Implementation

### Settings Configuration

```python
# shashtrarth/settings.py
SIMPLE_JWT = {
    "ACCESS_TOKEN_LIFETIME": timedelta(minutes=15),
    "REFRESH_TOKEN_LIFETIME": timedelta(days=7),
    "ROTATE_REFRESH_TOKENS": True,
    "BLACKLIST_AFTER_ROTATION": True,
    "UPDATE_LAST_LOGIN": True,
    "ALGORITHM": "HS256",
    "SIGNING_KEY": SECRET_KEY,
    "AUTH_HEADER_TYPES": ("Bearer",),
}
```

### Enhanced Token Refresh Views

All apps (students, contributor, customrcare) use the enhanced token refresh view:

```python
# authentication/jwt_utils.py
from authentication.jwt_utils import EnhancedTokenRefreshView

class TokenRefreshView(EnhancedTokenRefreshView):
    pass
```

### API Endpoints

- **Students**: `/api/students/token/refresh/`
- **Contributors**: `/api/contributor/token/refresh/`
- **Customer Care**: `/api/customrcare/token/refresh/`

### Response Format

```json
{
  "access": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refresh": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "Bearer",
  "expires_in": 900
}
```

## React Frontend Implementation

### Installation

```bash
npm install axios
```

### Usage

```javascript
import { useJWTAuth } from './hooks/useJWTAuth';

function App() {
  const { 
    isAuthenticated, 
    user, 
    loading, 
    login, 
    logout, 
    apiClient 
  } = useJWTAuth('students'); // or 'contributor', 'customrcare'

  const handleLogin = async () => {
    const result = await login({
      username: 'testuser',
      password: 'password123'
    });
    
    if (result.success) {
      console.log('Login successful');
    }
  };

  // Use apiClient for authenticated requests
  const fetchData = async () => {
    try {
      const response = await apiClient.get('/api/students/profile/');
      console.log(response.data);
    } catch (error) {
      console.error('API request failed:', error);
    }
  };

  if (loading) return <div>Loading...</div>;

  return (
    <div>
      {isAuthenticated ? (
        <div>
          <h1>Welcome, {user?.username}</h1>
          <button onClick={logout}>Logout</button>
          <button onClick={fetchData}>Fetch Data</button>
        </div>
      ) : (
        <button onClick={handleLogin}>Login</button>
      )}
    </div>
  );
}
```

### Features

- Automatic token refresh on API requests
- Secure localStorage token storage
- Request/response interceptors
- Authentication state management
- Error handling with retry logic

## React Native Implementation

### Installation

```bash
npm install @react-native-async-storage/async-storage react-native-keychain axios
```

### Usage

```javascript
import JWTAuthService from './services/JWTAuthService';
import { useEffect, useState } from 'react';

function App() {
  const [authState, setAuthState] = useState({
    isAuthenticated: false,
    user: null,
    loading: true
  });

  useEffect(() => {
    // Check initial auth status
    checkAuthStatus();
    
    // Listen for auth state changes
    const unsubscribe = JWTAuthService.addAuthStateListener((event) => {
      switch (event.type) {
        case 'LOGIN_SUCCESS':
          setAuthState({
            isAuthenticated: true,
            user: event.user,
            loading: false
          });
          break;
        case 'LOGOUT':
          setAuthState({
            isAuthenticated: false,
            user: null,
            loading: false
          });
          break;
      }
    });

    return unsubscribe;
  }, []);

  const checkAuthStatus = async () => {
    const status = await JWTAuthService.checkAuthStatus();
    setAuthState({
      isAuthenticated: status.isAuthenticated,
      user: status.user,
      loading: false
    });
  };

  const handleLogin = async () => {
    const result = await JWTAuthService.login({
      username: 'testuser',
      password: 'password123'
    }, 'students');
    
    if (result.success) {
      console.log('Login successful');
    }
  };

  const handleLogout = async () => {
    await JWTAuthService.logout();
  };

  // Use API client for requests
  const fetchData = async () => {
    try {
      const apiClient = JWTAuthService.getApiClient();
      const response = await apiClient.get('/api/students/profile/');
      console.log(response.data);
    } catch (error) {
      console.error('API request failed:', error);
    }
  };

  if (authState.loading) {
    return <LoadingScreen />;
  }

  return (
    <View>
      {authState.isAuthenticated ? (
        <AuthenticatedApp 
          user={authState.user} 
          onLogout={handleLogout}
          onFetchData={fetchData}
        />
      ) : (
        <LoginScreen onLogin={handleLogin} />
      )}
    </View>
  );
}
```

### Features

- Secure token storage using Keychain
- Automatic token refresh
- Auth state management with listeners
- Cross-platform compatibility
- Background token refresh

## Error Handling

### Error Codes

- `MISSING_REFRESH_TOKEN`: Refresh token not provided
- `INVALID_REFRESH_TOKEN`: Refresh token is invalid or expired
- `TOKEN_EXPIRED`: Access token has expired
- `INVALID_TOKEN`: Access token is invalid
- `AUTHORIZATION_REQUIRED`: No authorization header provided

### Error Response Format

```json
{
  "error": "invalid_refresh_token",
  "message": "The provided refresh token is invalid or expired",
  "code": "INVALID_REFRESH_TOKEN",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

## Testing

### Running Tests

```bash
# Run comprehensive test suite
python tests/test_jwt_authentication.py

# Run Django tests
python manage.py test authentication

# Check test results
cat tests/jwt_test_results.json
```

### Manual Testing with Curl

```bash
# Login
curl -X POST http://localhost:8000/api/students/login/ \
  -H "Content-Type: application/json" \
  -d '{"username": "testuser", "password": "password123"}'

# Refresh token
curl -X POST http://localhost:8000/api/students/token/refresh/ \
  -H "Content-Type: application/json" \
  -d '{"refresh": "your_refresh_token_here"}'

# Authenticated request
curl -X GET http://localhost:8000/api/students/profile/ \
  -H "Authorization: Bearer your_access_token_here"
```

## Security Considerations

1. **Token Storage**: Use secure storage (Keychain for mobile, secure cookies for web)
2. **HTTPS Only**: Always use HTTPS in production
3. **Token Rotation**: Enable token rotation to prevent replay attacks
4. **Logging**: Monitor authentication events for security
5. **Rate Limiting**: Implement rate limiting on auth endpoints
6. **Token Blacklisting**: Blacklist tokens on logout

## Troubleshooting

### Common Issues

1. **Token Refresh Fails**: Check refresh token validity and server connectivity
2. **CORS Issues**: Ensure proper CORS configuration for cross-origin requests
3. **Storage Issues**: Check storage permissions and available space
4. **Network Issues**: Implement proper retry logic and offline handling

### Debug Mode

Enable debug logging to troubleshoot issues:

```javascript
// React
localStorage.setItem('jwt_debug', 'true');

// React Native
// Check console logs for detailed error messages
```

## Migration Guide

If upgrading from a previous authentication system:

1. Update Django settings with new JWT configuration
2. Replace old token refresh views with enhanced versions
3. Update frontend code to use new authentication hooks/services
4. Test thoroughly with the provided test suite
5. Monitor logs for any authentication issues

## Implementation Status

✅ **Completed Features:**
- Short-lived access tokens (15 minutes)
- Long-lived refresh tokens (7 days)
- Automatic token rotation
- Enhanced token refresh views across all apps
- JWT authentication middleware
- React authentication hook with automatic refresh
- React Native authentication service with secure storage
- Comprehensive error handling with standardized error codes
- Complete test suite with curl examples
- Detailed documentation

✅ **Test Results:**
- Manual JWT test: All features working ✓
- Token generation: Working ✓
- Login API: Working ✓
- Token refresh: Working ✓
- Authenticated requests: Working ✓
- Automatic token refresh: Working ✓

## Quick Start

### 1. Backend Setup (Already Configured)
The Django backend is ready with:
- Updated JWT settings in `shashtrarth/settings.py`
- Enhanced token refresh views in all apps
- JWT utilities in `authentication/jwt_utils.py`

### 2. React Frontend Usage
```javascript
import { useJWTAuth } from './hooks/useJWTAuth';

const { isAuthenticated, login, apiClient } = useJWTAuth('students');
```

### 3. React Native Usage
```javascript
import JWTAuthService from './services/JWTAuthService';

const result = await JWTAuthService.login(credentials, 'students');
const apiClient = JWTAuthService.getApiClient();
```

### 4. Test the System
```bash
# Run comprehensive tests
python3 tests/test_jwt_authentication.py

# Run manual verification
python3 tests/manual_jwt_test.py
```

## Support

For issues or questions:
1. Check the troubleshooting section
2. Run the test suite to identify problems
3. Review server logs for detailed error information
4. Ensure all dependencies are properly installed
