from rest_framework import serializers
from django.contrib.auth.models import User
from .models import CustomrcareProfile, Ticket, FrontendError, TemporaryImage, WalkAroundImage, SOP
from django.contrib.auth import authenticate
from django.utils import timezone
from students.serializers import StudentSerializer
from students.models import Student
from shashtrarth.utils import validate_username, validate_password


class UserSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = [
            "id",
            "username",
            "email",
            "first_name",
            "last_name",
            "password",
        ]
        extra_kwargs = {"password": {"write_only": True}}

    def validate_username(self, value):
        """Validate username using centralized validation utility"""
        return validate_username(value)

    def validate_password(self, value):
        """Validate password using centralized validation utility"""
        return validate_password(value)


class CustomrcareProfileSerializer(serializers.ModelSerializer):
    user = UserSerializer()

    class Meta:
        model = CustomrcareProfile
        fields = ["user", "id", "role", "contact", "account_status", "slug"]

    def create(self, validated_data):
        user_data = validated_data.pop("user")

        # Validate username and password using security standards
        username = user_data.get('username')
        password = user_data.get('password')

        if username:
            validate_username(username)
        if password:
            validate_password(password)

        # Create the User instance
        user = User.objects.create_user(**user_data)

        profile = CustomrcareProfile.objects.create(user=user, **validated_data)
        return profile

    def update(self, instance, validated_data):
        user_data = validated_data.pop("user", None)

        if user_data:
            user = instance.user
            for attr, value in user_data.items():
                setattr(user, attr, value)
            user.save()

        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()

        return instance

    def to_internal_value(self, data):
        # Allow for an ID to be provided
        if isinstance(data, dict) and "id" in data:
            return {"id": data["id"]}
        elif isinstance(data, int):
            return {"id": data}
        return super().to_internal_value(data)


class LoginSerializer(serializers.Serializer):
    username = serializers.CharField(required=True)
    password = serializers.CharField(required=True, write_only=True)

    def validate(self, data):
        username = data.get("username")
        password = data.get("password")

        if username and password:
            user = authenticate(username=username, password=password)
            if user:
                if not user.is_active:
                    raise serializers.ValidationError("User account is disabled.")

                # Check if user has a customer care profile
                try:
                    customrcare_profile = user.customrcare_profile
                except CustomrcareProfile.DoesNotExist:
                    raise serializers.ValidationError(
                        "Access denied. This account is not authorized for customer care access."
                    )

                # Verify the role is correct
                if customrcare_profile.role != "customrcare":
                    raise serializers.ValidationError(
                        "Access denied. Invalid role for customer care access."
                    )

                return {
                    "user": user,
                    "profile": customrcare_profile,
                    "role": customrcare_profile.role,
                }
            else:
                raise serializers.ValidationError(
                    "Unable to log in with provided credentials."
                )
        else:
            raise serializers.ValidationError("Must include 'username' and 'password'.")


# class TicketSerializer(serializers.ModelSerializer):
#     ticket_assign = CustomrcareProfileSerializer(required=False, allow_null=True)
#     customer = CustomrcareProfileSerializer()
#     student = StudentSerializer()

#     class Meta:
#         model = Ticket
#         fields = [
#             "id",
#             "customer",
#             "student",
#             "ticket_assign",
#             "ticket_status",
#             "priority",
#             "subject",
#             "description",
#             "resolve_summary",
#             "attachments",
#             "tags",
#             "created_at",
#             "date_resolved",
#             "update_date",
#             "slug",
#         ]

#     def create(self, validated_data):
#         customer_data = validated_data.pop("customer")
#         student_data = validated_data.pop("student")
#         ticket_assign_data = validated_data.pop("ticket_assign", None)

#         customer_id = customer_data.get("id")
#         student_id = student_data.get("id")

#         # Fetch customer and student instances
#         customer = CustomrcareProfile.objects.get(id=customer_id)
#         student = Student.objects.get(id=student_id)

#         # Handle ticket assignment
#         ticket_assign = None
#         if ticket_assign_data:
#             ticket_assign = CustomrcareProfile.objects.get(id=ticket_assign_data["id"])

#         # Create the ticket
#         ticket = Ticket.objects.create(
#             customer=customer,
#             student=student,
#             ticket_assign=ticket_assign,
#             **validated_data,
#         )

#         return ticket
class TicketSerializer(serializers.ModelSerializer):
    customer_id = serializers.IntegerField(write_only=False)
    student_id = serializers.IntegerField(write_only=False)
    student= StudentSerializer(required=False)
    customer = CustomrcareProfileSerializer(required=False)
    ticket_assign_id = serializers.IntegerField(required=False, allow_null=True, write_only=False)

    class Meta:
        model = Ticket
        fields = [
            "id",
            "customer_id",
            "customer",
            "student_id",
            "student",
            "ticket_assign_id",
            "ticket_status",
            "priority",
            "subject",
            "description",
            "resolve_summary",
            "attachments",
            "tags",
            "created_at",
            "date_resolved",
            "update_date",
            "slug",
        ]

    def create(self, validated_data):
        customer_id = validated_data.pop("customer_id")
        student_id = validated_data.pop("student_id")
        ticket_assign_id = validated_data.pop("ticket_assign_id", None)

        # Fetch customer and student instances
        try:
            customer = CustomrcareProfile.objects.get(id=customer_id)
            student = Student.objects.get(id=student_id)
        except (CustomrcareProfile.DoesNotExist, Student.DoesNotExist):
            raise serializers.ValidationError("Invalid customer_id or student_id.")

        # Handle optional ticket assignment
        ticket_assign = None
        if ticket_assign_id:
            try:
                ticket_assign = CustomrcareProfile.objects.get(id=ticket_assign_id)
            except CustomrcareProfile.DoesNotExist:
                raise serializers.ValidationError("Invalid ticket_assign_id.")

        # Create the ticket
        ticket = Ticket.objects.create(
            customer=customer,
            student=student,
            ticket_assign=ticket_assign,
            **validated_data,
        )

        return ticket
    
    def update(self, instance, validated_data):
        customer_data = validated_data.pop("customer", None)
        student_data = validated_data.pop("student", None)
        ticket_assign_data = validated_data.pop("ticket_assign", None)

        if customer_data:
            for attr, value in customer_data.items():
                setattr(instance.customer, attr, value)
            instance.customer.save()

        if student_data:
            for attr, value in student_data.items():
                setattr(instance.student, attr, value)
            instance.student.save()

        if ticket_assign_data:
            ticket_assign = CustomrcareProfile.objects.get(id=ticket_assign_data["id"])
            instance.ticket_assign = ticket_assign
            instance.ticket_assign.save()

        # Update the Ticket instance fields
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()
        return instance
    
class FrontendErrorSerializer(serializers.ModelSerializer):
    """Enhanced serializer for frontend errors"""
    user = UserSerializer(read_only=True)
    resolved_by = UserSerializer(read_only=True)

    class Meta:
        model = FrontendError
        fields = [
            'id', 'error_type', 'severity', 'error_message', 'stack_trace',
            'user', 'session_id', 'user_agent', 'ip_address',
            'browser_name', 'browser_version', 'device_type', 'screen_resolution',
            'page_url', 'page_title', 'referrer_url',
            'component_name', 'function_name', 'line_number', 'column_number',
            'error_data', 'user_actions', 'console_logs',
            'resolved', 'resolved_by', 'resolved_at', 'resolution_notes',
            'occurrence_count', 'first_occurrence', 'last_occurrence', 'created_at'
        ]
        read_only_fields = ['id', 'first_occurrence', 'last_occurrence', 'created_at']


class FrontendErrorSummarySerializer(serializers.Serializer):
    """Serializer for frontend error summary data"""
    total_errors = serializers.IntegerField()
    by_type = serializers.ListField()
    by_severity = serializers.ListField()
    by_browser = serializers.ListField()
    by_page = serializers.ListField()
    unresolved_count = serializers.IntegerField()
    critical_count = serializers.IntegerField()
    recent_errors = FrontendErrorSerializer(many=True)


class FrontendErrorCreateSerializer(serializers.Serializer):
    """Serializer for creating frontend errors from JavaScript"""
    error_message = serializers.CharField(max_length=2000)
    error_type = serializers.ChoiceField(
        choices=FrontendError.ERROR_TYPES,
        required=False
    )
    severity = serializers.ChoiceField(
        choices=FrontendError.SEVERITY_LEVELS,
        required=False
    )
    stack_trace = serializers.CharField(required=False, allow_blank=True)
    page_url = serializers.URLField(max_length=1000)
    page_title = serializers.CharField(max_length=500, required=False, allow_blank=True)
    referrer_url = serializers.URLField(max_length=1000, required=False, allow_blank=True)
    component_name = serializers.CharField(max_length=200, required=False, allow_blank=True)
    function_name = serializers.CharField(max_length=200, required=False, allow_blank=True)
    line_number = serializers.IntegerField(required=False, allow_null=True)
    column_number = serializers.IntegerField(required=False, allow_null=True)
    screen_resolution = serializers.CharField(max_length=20, required=False, allow_blank=True)
    additional_data = serializers.JSONField(required=False)
    user_actions = serializers.ListField(required=False)
    console_logs = serializers.ListField(required=False)

# serializers.py
from django.conf import settings
class TemporaryImageSerializer(serializers.ModelSerializer):
    class Meta:
        model = TemporaryImage
        fields = ['id', 'image', 'uploaded_at', 'expires_at']
        read_only_fields = ['uploaded_at', 'expires_at']

    def get_image_url(self, obj):
        request = self.context.get('request')
        if request is not None:
            return request.build_absolute_uri(obj.image.url)
        return f"{settings.MEDIA_URL}{obj.image.url}"


class WalkAroundImageSerializer(serializers.ModelSerializer):
    """Serializer for WalkAroundImage model with optional user association and status management."""

    image_url = serializers.SerializerMethodField()
    user_username = serializers.SerializerMethodField()

    class Meta:
        model = WalkAroundImage
        fields = [
            'id', 'user', 'user_username', 'image', 'image_url',
            'title', 'description', 'status', 'created_at', 'updated_at'
        ]
        read_only_fields = ['user', 'created_at', 'updated_at']

    def get_user_username(self, obj):
        """Get username or 'Anonymous' if no user."""
        return obj.user.username if obj.user else "Anonymous"

    def get_image_url(self, obj):
        """Get full URL for the image."""
        request = self.context.get('request')
        if request is not None and obj.image:
            return request.build_absolute_uri(obj.image.url)
        return f"{settings.MEDIA_URL}{obj.image.url}" if obj.image else None

    def create(self, validated_data):
        """Create a new walk-around image with user from request context if authenticated."""
        request = self.context.get('request')
        if request and request.user and request.user.is_authenticated:
            validated_data['user'] = request.user
        else:
            validated_data['user'] = None  # Anonymous user
        return super().create(validated_data)

    def validate_status(self, value):
        """Validate status field."""
        if value not in ['active', 'inactive']:
            raise serializers.ValidationError("Status must be either 'active' or 'inactive'.")
        return value

    def validate(self, data):
        """Additional validation for the entire object."""
        # If setting status to active, check the global limit (5 active images total)
        if data.get('status') == 'active':
            # Count current active images globally
            active_count = WalkAroundImage.objects.filter(
                status='active'
            ).count()

            # If this is an update, exclude current instance
            if self.instance:
                active_count = WalkAroundImage.objects.filter(
                    status='active'
                ).exclude(pk=self.instance.pk).count()

            # Check if adding this would exceed the limit
            if active_count >= 5:
                # This will be handled in the model's save method
                pass

        return data


# ============================================================================
# SOP SERIALIZERS
# ============================================================================

class SOPSerializer(serializers.ModelSerializer):
    """Serializer for SOP model with role-based access"""

    access_display = serializers.CharField(source='get_access_display', read_only=True)
    created_by_username = serializers.CharField(source='created_by.username', read_only=True)
    updated_by_username = serializers.CharField(source='updated_by.username', read_only=True)

    class Meta:
        model = SOP
        fields = [
            'id', 'name', 'pdf', 'access', 'access_display',
            'created', 'last_update', 'created_by', 'created_by_username',
            'updated_by', 'updated_by_username', 'slug'
        ]
        read_only_fields = [
            'id', 'created', 'last_update', 'created_by', 'updated_by', 'slug'
        ]

    def validate_pdf(self, value):
        """Validate PDF file"""
        if value:
            # Check file size (max 10MB)
            if value.size > 10 * 1024 * 1024:
                raise serializers.ValidationError("PDF file size cannot exceed 10MB")

            # Check file extension
            if not value.name.lower().endswith('.pdf'):
                raise serializers.ValidationError("Only PDF files are allowed")

        return value


class SOPAdminSerializer(SOPSerializer):
    """Admin serializer for SOP with full access"""

    class Meta(SOPSerializer.Meta):
        # Admin can modify all fields except timestamps and auto-generated fields
        read_only_fields = [
            'id', 'created', 'last_update', 'slug'
        ]


class SOPPublicSerializer(serializers.ModelSerializer):
    """Public serializer for SOP with minimal fields"""

    access_display = serializers.CharField(source='get_access_display', read_only=True)

    class Meta:
        model = SOP
        fields = [
            'id', 'name', 'pdf', 'access', 'access_display',
            'created', 'last_update'
        ]
        read_only_fields = [
            'id', 'name', 'pdf', 'access', 'access_display',
            'created', 'last_update'
        ]
